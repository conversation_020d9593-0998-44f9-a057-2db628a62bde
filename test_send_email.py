import smtplib
from email.mime.text import MIMEText
from email.header import Head<PERSON>
from email.utils import formataddr
import os

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(override=True)

# Define to/from
sender = '<EMAIL>'
sender_title = "<PERSON>"
recipient = '<EMAIL>'

# Create message
msg = MIMEText("Message text", 'plain', 'utf-8')
msg['Subject'] =  Header("Sent from python", 'utf-8')
msg['From'] = formataddr((str(Header(sender_title, 'utf-8')), sender))
msg['To'] = recipient

# Create server object with SSL option
# Change below smtp.zoho.com, corresponds to your location in the world. 
# For instance smtp.zoho.eu if you are in Europe or smtp.zoho.in if you are in India.
server = smtplib.SMTP_SSL('smtp.zoho.eu', 465)

# Perform operations via server
server.login(sender, os.getenv('ZOHO_APP_PASSWORD'))
server.sendmail(sender, [recipient], msg.as_string())
server.quit()