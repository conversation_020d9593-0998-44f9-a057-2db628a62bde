#!/usr/bin/env python3
"""
Production email scheduler service.
Handles daily preprocessing and email sending within business hours.
"""
import datetime
import threading
import time
import schedule
from typing import Dict, Any, Optional

from app.scheduling.daily_preprocessor import DailyPreprocessor
from app.scheduling.quota_manager import EmailItem
from app.server.queue import send_single_email
from app.config import SCHEDULING_CONFIG
from app.utils.logger import setup_logger

logger = setup_logger('scheduler_service')

class SchedulerService:
    """Production email scheduler with quota management and business hours enforcement."""
    
    def __init__(self):
        self.preprocessor: Optional[DailyPreprocessor] = None
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.sender_thread: Optional[threading.Thread] = None
        self.config = SCHEDULING_CONFIG
        self.current_date: Optional[datetime.date] = None
        self.daily_stats: Dict[str, Any] = {}
        
    def start(self):
        """Start the scheduler service."""
        if self.is_running:
            logger.warning("Scheduler service is already running")
            return
        
        self.is_running = True
        logger.info("Starting email scheduler service")
        
        # Schedule daily preprocessing
        schedule.every().day.at(f"{self.config['preprocessing']['run_hour']:02d}:{self.config['preprocessing']['run_minute']:02d}").do(self._run_daily_preprocessing)
        
        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        # Start email sender thread
        self.sender_thread = threading.Thread(target=self._sender_loop, daemon=True)
        self.sender_thread.start()
        
        # Run initial preprocessing if it's past the scheduled time today
        self._check_and_run_initial_preprocessing()
        
        logger.info("Email scheduler service started successfully")
    
    def stop(self):
        """Stop the scheduler service."""
        if not self.is_running:
            return
        
        logger.info("Stopping email scheduler service")
        self.is_running = False
        
        # Wait for threads to finish
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        if self.sender_thread and self.sender_thread.is_alive():
            self.sender_thread.join(timeout=5)
        
        logger.info("Email scheduler service stopped")
    
    def _scheduler_loop(self):
        """Main scheduler loop that runs scheduled tasks."""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error in scheduler loop: {str(e)}")
                time.sleep(60)
    
    def _sender_loop(self):
        """Email sender loop that processes emails during business hours."""
        while self.is_running:
            try:
                current_time = datetime.datetime.now()
                
                # Only send emails during business hours
                if not self._is_business_hours(current_time):
                    time.sleep(300)  # Check every 5 minutes outside business hours
                    continue
                
                # Check if we have a preprocessor for today
                if not self.preprocessor or self.current_date != current_time.date():
                    time.sleep(60)  # Wait for preprocessing to complete
                    continue
                
                # Process emails for all accounts
                emails_sent = self._process_pending_emails()
                
                if emails_sent == 0:
                    time.sleep(60)  # No emails to send, wait 1 minute
                else:
                    time.sleep(30)  # Emails sent, wait 30 seconds before next batch
                    
            except Exception as e:
                logger.error(f"Error in sender loop: {str(e)}")
                time.sleep(60)
    
    def _run_daily_preprocessing(self):
        """Run daily preprocessing."""
        try:
            logger.info("Starting daily email preprocessing")
            
            self.preprocessor = DailyPreprocessor()
            self.current_date = datetime.date.today()
            
            # Run preprocessing
            results = self.preprocessor.run_daily_preprocessing(self.current_date)
            self.daily_stats = results
            
            logger.info(f"Daily preprocessing completed: {results['emails_scheduled']} emails scheduled, "
                       f"{results['emails_postponed']} emails postponed")
            
        except Exception as e:
            logger.error(f"Error in daily preprocessing: {str(e)}")
            self.preprocessor = None
    
    def _check_and_run_initial_preprocessing(self):
        """Check if we need to run preprocessing for today."""
        current_time = datetime.datetime.now()
        preprocessing_time = current_time.replace(
            hour=self.config['preprocessing']['run_hour'],
            minute=self.config['preprocessing']['run_minute'],
            second=0,
            microsecond=0
        )
        
        # If it's past preprocessing time and we haven't run it today, run it now
        if current_time >= preprocessing_time and self.current_date != current_time.date():
            logger.info("Running initial preprocessing for today")
            self._run_daily_preprocessing()
    
    def _is_business_hours(self, dt: datetime.datetime) -> bool:
        """Check if current time is within business hours."""
        hour = dt.hour
        start_hour = self.config['business_hours']['start_hour']
        end_hour = self.config['business_hours']['end_hour']
        return start_hour <= hour < end_hour
    
    def _process_pending_emails(self) -> int:
        """Process pending emails for all accounts. Returns number of emails sent."""
        if not self.preprocessor:
            return 0
        
        emails_sent = 0
        current_time = datetime.datetime.now()
        
        # Get all accounts that have emails in their queues
        for account_id in list(self.preprocessor.daily_queues.keys()):
            try:
                # Get next email for this account
                next_email = self._get_next_email_for_account(account_id, current_time)
                
                if next_email:
                    # Send the email
                    success = self._send_email(next_email)
                    if success:
                        emails_sent += 1
                        logger.info(f"Sent email {next_email.email_id} from account {account_id}")
                    else:
                        logger.error(f"Failed to send email {next_email.email_id} from account {account_id}")
                        # Put the email back in the queue with a delay
                        self._reschedule_failed_email(next_email)
                        
            except Exception as e:
                logger.error(f"Error processing emails for account {account_id}: {str(e)}")
        
        return emails_sent
    
    def _get_next_email_for_account(self, account_id: str, current_time: datetime.datetime) -> Optional[EmailItem]:
        """Get the next email to send for an account, if it's time to send it."""
        if account_id not in self.preprocessor.daily_queues:
            return None
        
        queue = self.preprocessor.daily_queues[account_id]
        if not queue:
            return None
        
        # Peek at the next email (don't remove it yet)
        _, next_email = queue[0]
        
        # Check if it's time to send this email
        if next_email.scheduled_time <= current_time:
            # Remove and return the email
            return self.preprocessor.get_next_email_for_account(account_id)
        
        return None
    
    def _send_email(self, email_item: EmailItem) -> bool:
        """Send an email using the existing email sending infrastructure."""
        try:
            # Convert EmailItem back to the format expected by send_single_email
            email_data = {
                'id': email_item.email_id,
                'account_id': email_item.account_id,
                'is_follow_up': email_item.is_followup,
                'follow_up': 1 if email_item.is_followup else 0  # Simplified for now
            }
            
            # Use the existing send_single_email function
            return send_single_email(email_data)
            
        except Exception as e:
            logger.error(f"Error sending email {email_item.email_id}: {str(e)}")
            return False
    
    def _reschedule_failed_email(self, email_item: EmailItem):
        """Reschedule a failed email with a delay."""
        try:
            # Add 10 minutes delay
            email_item.scheduled_time += datetime.timedelta(minutes=10)
            
            # Put it back in the queue
            self.preprocessor._add_to_daily_queue(email_item)
            
            logger.info(f"Rescheduled failed email {email_item.email_id} for {email_item.scheduled_time}")
            
        except Exception as e:
            logger.error(f"Error rescheduling email {email_item.email_id}: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the scheduler service."""
        current_time = datetime.datetime.now()
        
        status = {
            'is_running': self.is_running,
            'current_time': current_time.isoformat(),
            'current_date': self.current_date.isoformat() if self.current_date else None,
            'is_business_hours': self._is_business_hours(current_time),
            'preprocessor_ready': self.preprocessor is not None,
            'daily_stats': self.daily_stats,
            'queue_status': {}
        }
        
        if self.preprocessor:
            for account_id, queue in self.preprocessor.daily_queues.items():
                status['queue_status'][account_id] = {
                    'emails_remaining': len(queue),
                    'next_email_time': queue[0][1].scheduled_time.isoformat() if queue else None
                }
        
        return status
    
    def force_preprocessing(self) -> Dict[str, Any]:
        """Force run preprocessing (for testing/manual trigger)."""
        logger.info("Force running daily preprocessing")
        self._run_daily_preprocessing()
        return self.daily_stats

# Global scheduler service instance
scheduler_service = SchedulerService()
