#!/usr/bin/env python3
"""
Keep-alive mechanism to prevent Render free tier from sleeping
"""
import threading
import time
import requests
import datetime
import random
from app.config import SERVER_CONFIG
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('utils.keep_alive')

# Global flag to track if ping-pong cycle is active
ping_pong_active = False

# Global flag to indicate if the server is shutting down
is_shutting_down = False

# Lock for thread-safe operations
lock = threading.Lock()

# Current ping/pong timer
current_timer = None

def start_ping_pong_cycle():
    """
    Start the ping-pong cycle to keep the server awake.
    This should be called when the server starts.
    """
    global ping_pong_active, is_shutting_down

    with lock:
        if ping_pong_active:
            logger.info("Ping-pong cycle already active, not starting a new one")
            return

        ping_pong_active = True
        is_shutting_down = False

    logger.info("Starting ping-pong cycle to keep server awake")

    # Start with a ping after a short delay to allow the server to fully initialize
    threading.Thread(target=_make_initial_ping, daemon=True).start()

def stop_ping_pong_cycle():
    """
    Stop the ping-pong cycle.
    This should be called when the server is shutting down.
    """
    global ping_pong_active, is_shutting_down, current_timer

    with lock:
        if not ping_pong_active:
            logger.info("Ping-pong cycle not active, nothing to stop")
            return

        logger.info("Stopping ping-pong cycle")
        is_shutting_down = True
        ping_pong_active = False

        # Cancel the current timer if it exists
        if current_timer is not None and current_timer.is_alive():
            logger.info("Canceling current ping/pong timer")
            current_timer.cancel()

    logger.info("Ping-pong cycle stopped")

def _make_initial_ping():
    """Make the initial ping request after a short delay."""
    # Wait for the server to fully initialize
    time.sleep(5)

    # Use the improved _delayed_request function with retry logic
    # but with a 0 second delay since we've already waited
    _delayed_request('/ping', 0)

def _delayed_request(endpoint, delay_seconds=0):
    """
    Make a request to the specified endpoint after a delay.

    Args:
        endpoint (str): The endpoint to request ('/ping' or '/pong')
        delay_seconds (int, optional): Seconds to wait before making the request. Default is 0.
    """
    # Check if we're shutting down before sleeping
    if is_shutting_down:
        logger.info(f"Server is shutting down, skipping {endpoint} request")
        return

    # Only sleep if there's a delay
    if delay_seconds > 0:
        time.sleep(delay_seconds)

    # Check again after sleeping
    if is_shutting_down:
        logger.info(f"Server is shutting down, skipping {endpoint} request")
        return

    # Retry up to 3 times with increasing delays
    max_retries = 3
    retry_delay = 5  # Start with 5 seconds

    for attempt in range(1, max_retries + 1):
        # Check if we're shutting down before each attempt
        if is_shutting_down:
            logger.info(f"Server is shutting down, skipping {endpoint} request")
            return

        try:
            # Use localhost URL instead of the public URL for internal requests
            # This ensures it works even if the public URL is not accessible
            url = f"http://localhost:{SERVER_CONFIG['port']}{endpoint}"
            logger.debug(f"Making request to {url} (attempt {attempt}/{max_retries})")

            # Reduce timeout to avoid long waits
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                logger.debug(f"Successfully called {endpoint}: {response.status_code}")
                return  # Success, exit the function
            else:
                logger.warning(f"Unexpected response from {endpoint}: {response.status_code}")

        except requests.exceptions.ConnectionError as e:
            logger.warning(f"Connection error on attempt {attempt}/{max_retries} to {endpoint}: {str(e)}")
        except Exception as e:
            logger.error(f"Failed to make request to {endpoint} (attempt {attempt}/{max_retries}): {str(e)}")

        # If we haven't reached max retries and not shutting down, wait and try again
        if attempt < max_retries and not is_shutting_down:
            logger.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
            retry_delay *= 2  # Exponential backoff
        else:
            # If all retries fail and this is a ping and not shutting down, restart the cycle
            if endpoint == '/ping' and not is_shutting_down:
                logger.info("All retry attempts failed. Attempting to restart ping-pong cycle")
                global current_timer
                current_timer = threading.Timer(60, _delayed_request, args=('/ping',))
                current_timer.daemon = True
                current_timer.start()

def handle_ping():
    """
    Handle a ping request and schedule a pong.

    Returns:
        dict: Response data
    """
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"Ping received at {timestamp}")

    # Schedule a pong after ~14 minutes (just under Render's 15-min limit)
    # Add some randomness to avoid exact timing
    delay = 840 + random.randint(-30, 30)  # 14 minutes ± 30 seconds
    next_time = (datetime.datetime.now() + datetime.timedelta(seconds=delay)).strftime("%Y-%m-%d %H:%M:%S")

    # Only schedule if not shutting down
    if not is_shutting_down:
        global current_timer
        current_timer = threading.Timer(delay, _delayed_request, args=('/pong',))
        current_timer.daemon = True
        current_timer.start()

    return {
        "status": "ping received",
        "timestamp": timestamp,
        "next": "pong",
        "next_time": next_time
    }

def handle_pong():
    """
    Handle a pong request and schedule a ping.

    Returns:
        dict: Response data
    """
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"Pong received at {timestamp}")

    # Schedule a ping after ~14 minutes
    # Add some randomness to avoid exact timing
    delay = 840 + random.randint(-30, 30)  # 14 minutes ± 30 seconds
    next_time = (datetime.datetime.now() + datetime.timedelta(seconds=delay)).strftime("%Y-%m-%d %H:%M:%S")

    # Only schedule if not shutting down
    if not is_shutting_down:
        global current_timer
        current_timer = threading.Timer(delay, _delayed_request, args=('/ping',))
        current_timer.daemon = True
        current_timer.start()

    return {
        "status": "pong received",
        "timestamp": timestamp,
        "next": "ping",
        "next_time": next_time
    }

# Email scheduling functionality
def schedule_daily_emails(sender_func):
    """
    Schedule 10 random emails between 9am and 5pm.

    Args:
        sender_func (callable): Function to call to send an email
    """
    # Get current date
    today = datetime.datetime.now().date()

    # Define business hours (9am to 5pm)
    business_start = datetime.datetime.combine(today, datetime.time(9, 0))
    business_end = datetime.datetime.combine(today, datetime.time(17, 0))

    # Calculate total seconds in business hours
    total_seconds = (business_end - business_start).total_seconds()

    # Generate 10 random times within business hours
    email_times = []
    for _ in range(10):
        random_seconds = random.randint(0, int(total_seconds))
        email_time = business_start + datetime.timedelta(seconds=random_seconds)
        email_times.append(email_time)

    # Sort the times
    email_times.sort()

    # Log the schedule
    logger.info(f"Scheduled 10 emails for today ({today}):")
    for i, email_time in enumerate(email_times):
        logger.info(f"  Email #{i+1}: {email_time.strftime('%H:%M:%S')}")

    # Schedule each email
    for i, email_time in enumerate(email_times):
        # Calculate seconds from now until send time
        now = datetime.datetime.now()
        if email_time > now:
            delay_seconds = (email_time - now).total_seconds()

            # Schedule the email
            threading.Timer(delay_seconds, sender_func,
                           args=[f"recipient_{i}@example.com",
                                 f"Scheduled Email #{i+1}",
                                 f"This is scheduled email #{i+1} for today."]).start()

            logger.info(f"Scheduled email #{i+1} for {email_time}")
