#!/usr/bin/env python3
"""
Test script to retrieve data from the Supabase database
"""
import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(override=True)

# Add the current directory to the path so we can import the app modules
sys.path.append('.')

from app.utils.logger import setup_logger
import supabase

# Set up logger
logger = setup_logger()

def main():
    """Main function to test retrieving data from Supabase."""
    # Get Supabase credentials from environment
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    
    print("Supabase configuration:")
    print(f"URL: {supabase_url}")
    print(f"Key: {supabase_key[:10]}... (truncated)")
    
    # Create Supabase client
    print("\nConnecting to Supabase...")
    client = supabase.create_client(supabase_url, supabase_key)
    
    # Try to retrieve recipients
    print("\nRetrieving recipients...")
    try:
        response = client.table('recipients').select('*').execute()
        recipients = response.data
        
        print(f"Found {len(recipients)} recipients:")
        for r in recipients:
            print(f"- {r.get('email', 'No email')}, ID: {r.get('id', 'No ID')}")
    except Exception as e:
        print(f"Error retrieving recipients: {str(e)}")
    
    # Try to retrieve email accounts
    print("\nRetrieving email accounts...")
    try:
        response = client.table('email_accounts').select('*').execute()
        accounts = response.data
        
        print(f"Found {len(accounts)} email accounts:")
        for a in accounts:
            print(f"- {a.get('email', 'No email')}, ID: {a.get('id', 'No ID')}")
    except Exception as e:
        print(f"Error retrieving email accounts: {str(e)}")
    
    # Try to retrieve email queue
    print("\nRetrieving email queue...")
    try:
        response = client.table('email_queue').select('*').execute()
        queue = response.data
        
        print(f"Found {len(queue)} emails in queue:")
        for q in queue:
            print(f"- Subject: {q.get('subject', 'No subject')}, Status: {q.get('status', 'No status')}")
    except Exception as e:
        print(f"Error retrieving email queue: {str(e)}")
    
    # Try to retrieve server status
    print("\nRetrieving server status...")
    try:
        response = client.table('server_status').select('*').execute()
        status = response.data
        
        print(f"Found {len(status)} server status records:")
        for s in status:
            print(f"- Status: {s.get('status', 'No status')}, Last ping: {s.get('last_ping', 'No ping')}")
    except Exception as e:
        print(f"Error retrieving server status: {str(e)}")
    
    # Try to call RPC function
    print("\nCalling RPC function get_emails_to_send...")
    try:
        response = client.rpc('get_emails_to_send', {'time_window_minutes': 60}).execute()
        emails = response.data
        
        print(f"Found {len(emails)} emails to send:")
        for e in emails:
            print(f"- Recipient: {e.get('recipient_email', 'No recipient')}, Subject: {e.get('subject', 'No subject')}")
    except Exception as e:
        print(f"Error calling RPC function: {str(e)}")

if __name__ == "__main__":
    main()
