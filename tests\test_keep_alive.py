#!/usr/bin/env python3
"""
Tests for the keep-alive module
"""
import unittest
from unittest.mock import patch, MagicMock, call
import os
import sys
import datetime
import threading
import time

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.utils.keep_alive import (
    start_ping_pong_cycle,
    handle_ping,
    handle_pong,
    ping_pong_active,
    _delayed_request
)
from app.config import SERVER_CONFIG

class TestKeepAlive(BaseTestCase):
    """Test cases for the keep-alive module."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Mock threading.Thread
        self.thread_patcher = patch('threading.Thread')
        self.mock_thread = self.thread_patcher.start()
        self.mock_thread_instance = MagicMock()
        self.mock_thread.return_value = self.mock_thread_instance
        
        # Mock requests.get
        self.requests_patcher = patch('requests.get')
        self.mock_requests = self.requests_patcher.start()
        self.mock_response = MagicMock()
        self.mock_response.status_code = 200
        self.mock_response.json.return_value = {'status': 'success'}
        self.mock_requests.return_value = self.mock_response
        
        # Mock datetime.datetime
        self.datetime_patcher = patch('datetime.datetime')
        self.mock_datetime = self.datetime_patcher.start()
        self.mock_now = MagicMock()
        self.mock_now.strftime.return_value = '2021-01-01 00:00:00'
        self.mock_datetime.now.return_value = self.mock_now
        
        # Mock time.sleep
        self.sleep_patcher = patch('time.sleep')
        self.mock_sleep = self.sleep_patcher.start()
        
        # Mock random.randint
        self.random_patcher = patch('random.randint')
        self.mock_random = self.random_patcher.start()
        self.mock_random.return_value = 0  # No randomness in tests
        
        # Reset ping_pong_active
        global ping_pong_active
        ping_pong_active = False
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        self.thread_patcher.stop()
        self.requests_patcher.stop()
        self.datetime_patcher.stop()
        self.sleep_patcher.stop()
        self.random_patcher.stop()
    
    def test_start_ping_pong_cycle(self):
        """Test starting the ping-pong cycle."""
        # Call the function
        start_ping_pong_cycle()
        
        # Check that a thread was started
        self.mock_thread.assert_called_once()
        self.mock_thread_instance.start.assert_called_once()
        
        # Check that ping_pong_active was set to True
        self.assertTrue(ping_pong_active)
        
        # Call the function again (should not start another thread)
        self.mock_thread.reset_mock()
        start_ping_pong_cycle()
        
        # Check that no new thread was started
        self.mock_thread.assert_not_called()
    
    def test_handle_ping(self):
        """Test handling a ping request."""
        # Call the function
        result = handle_ping()
        
        # Check the result
        self.assertEqual(result['status'], 'ping received')
        self.assertEqual(result['timestamp'], '2021-01-01 00:00:00')
        self.assertEqual(result['next'], 'pong')
        self.assertEqual(result['next_time'], '2021-01-01 00:00:00')
        
        # Check that a thread was started for the delayed pong
        self.mock_thread.assert_called_once()
        self.mock_thread_instance.start.assert_called_once()
    
    def test_handle_pong(self):
        """Test handling a pong request."""
        # Call the function
        result = handle_pong()
        
        # Check the result
        self.assertEqual(result['status'], 'pong received')
        self.assertEqual(result['timestamp'], '2021-01-01 00:00:00')
        self.assertEqual(result['next'], 'ping')
        self.assertEqual(result['next_time'], '2021-01-01 00:00:00')
        
        # Check that a thread was started for the delayed ping
        self.mock_thread.assert_called_once()
        self.mock_thread_instance.start.assert_called_once()
    
    def test_delayed_request(self):
        """Test the _delayed_request function."""
        # Call the function directly
        _delayed_request('/ping', 1)
        
        # Check that time.sleep was called
        self.mock_sleep.assert_called_once_with(1)
        
        # Check that requests.get was called with the correct URL
        self.mock_requests.assert_called_once_with(f"{SERVER_CONFIG['base_url']}/ping")
        
        # Test with an error response
        self.mock_sleep.reset_mock()
        self.mock_requests.reset_mock()
        self.mock_response.status_code = 500
        
        # Call the function again
        _delayed_request('/ping', 1)
        
        # Check that time.sleep was called
        self.mock_sleep.assert_called_once_with(1)
        
        # Check that requests.get was called with the correct URL
        self.mock_requests.assert_called_once_with(f"{SERVER_CONFIG['base_url']}/ping")

if __name__ == '__main__':
    unittest.main()
