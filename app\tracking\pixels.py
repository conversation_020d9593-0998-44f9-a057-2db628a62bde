#!/usr/bin/env python3
"""
Tracking pixel generation and handling
"""
import time
import uuid
import base64
from app.config import SERVER_CONFIG
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('tracking.pixels')

# Tracking pixel images (base64 encoded)
TRACKING_PIXELS = {
    'red': 'R0lGODlhAQABAIAAAP8AADAAACwAAAAAAQABAAACAkQBADs=',  # Red 1x1 GIF
    'blue': 'R0lGODlhAQABAIAAAAEBAQAAACwAAAAAAQABAAACAkQBADs=',  # Blue 1x1 GIF
    'transparent': 'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',  # Transparent 1x1 GIF
    'logo': 'R0lGODlhEAAQAMQAAORHHOVSKudfOulrSOp3WOyDZu6QdvCchPGolfO0o/XBs/fNwfjZ0frl3/zy7////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAkAABAALAAAAAAQABAAAAVVICSOZGlCQAosJ6mu7fiyZeKqNKToQGDsM8hBADgUXoGAiqhSvp5QAnQKGIgUhwFUYLCVDFCrKUE1lBavAViFIDlTImbKC5Gm2hB0SlBCBMQiB0UjIQA7'  # Small logo GIF
}

def generate_tracking_id():
    """
    Generate a unique tracking ID for an email.
    
    Returns:
        str: Unique tracking ID
    """
    return str(uuid.uuid4())

def get_tracking_pixel_url(tracking_id, pixel_type='transparent', timestamp=None):
    """
    Generate a URL for a tracking pixel.
    
    Args:
        tracking_id (str): Unique identifier for the email
        pixel_type (str): Type of pixel to use ('red', 'blue', 'transparent', 'logo')
        timestamp (int, optional): Timestamp to add to URL to prevent caching
        
    Returns:
        str: URL for the tracking pixel
    """
    if timestamp is None:
        timestamp = int(time.time())
        
    pixel_filename = {
        'red': 'pixel.gif',
        'blue': 'blue.gif',
        'transparent': 'transparent.gif',
        'logo': 'logo.gif'
    }.get(pixel_type, 'pixel.gif')
    
    return f"{SERVER_CONFIG['base_url']}/track/{tracking_id}/{pixel_filename}?t={timestamp}"

def get_tracking_pixel_data(pixel_type='transparent'):
    """
    Get the base64-encoded data for a tracking pixel.
    
    Args:
        pixel_type (str): Type of pixel to use ('red', 'blue', 'transparent', 'logo')
        
    Returns:
        bytes: Binary data for the tracking pixel
    """
    pixel_data = TRACKING_PIXELS.get(pixel_type, TRACKING_PIXELS['transparent'])
    return base64.b64decode(pixel_data)

def add_tracking_pixel(html_content, tracking_id):
    """
    Add a tracking pixel to HTML content.
    
    Args:
        html_content (str): HTML content of the email
        tracking_id (str): Unique identifier for the email
        
    Returns:
        str: HTML content with tracking pixel added
    """
    timestamp = int(time.time())
    pixel_url = get_tracking_pixel_url(tracking_id, 'transparent', timestamp)
    
    # Create the pixel HTML
    pixel_html = f'<img src="{pixel_url}" width="1" height="1" alt="" style="display:none;width:1px;height:1px;"/>'
    
    # Add the pixel to the HTML content
    if '</body>' in html_content:
        return html_content.replace('</body>', f'{pixel_html}</body>')
    elif '</html>' in html_content:
        return html_content.replace('</html>', f'{pixel_html}</html>')
    else:
        return html_content + pixel_html

def generate_test_pixels(tracking_id=None):
    """
    Generate test tracking pixels for testing.
    
    Args:
        tracking_id (str, optional): Tracking ID to use, generates a new one if not provided
        
    Returns:
        dict: Dictionary with tracking ID and URLs for different pixel types
    """
    if tracking_id is None:
        tracking_id = generate_tracking_id()
        
    timestamp = int(time.time())
    
    # Generate URLs for different pixel types
    red_url = get_tracking_pixel_url(tracking_id, 'red', timestamp)
    blue_url = get_tracking_pixel_url(tracking_id, 'blue', timestamp)
    logo_url = get_tracking_pixel_url(tracking_id, 'logo', timestamp)
    transparent_url = get_tracking_pixel_url(tracking_id, 'transparent', timestamp)
    
    logger.info(f"Testing tracking pixels for ID: {tracking_id}")
    logger.info(f"Red box pixel: {red_url}")
    logger.info(f"Blue box pixel: {blue_url}")
    logger.info(f"Logo pixel: {logo_url}")
    logger.info(f"Transparent pixel: {transparent_url}")
    logger.info(f"To manually test, open any of these URLs in your browser")
    
    # Return the tracking ID and URLs for reference
    return {
        "tracking_id": tracking_id,
        "red_url": red_url,
        "blue_url": blue_url,
        "logo_url": logo_url,
        "transparent_url": transparent_url,
        "check_url": f"{SERVER_CONFIG['base_url']}/tracking/{tracking_id}"
    }
