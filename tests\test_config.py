#!/usr/bin/env python3
"""
Test configuration for the Cold Email Server unit tests
"""
import os
import sys
import unittest
from unittest.mock import patch, MagicMock
from dotenv import load_dotenv

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
load_dotenv(override=True)

# Test configuration
TEST_CONFIG = {
    'email': {
        'smtp_server': 'smtp.zoho.eu',
        'smtp_port': 465,
        'sender_email': '<EMAIL>',
        'app_password': 'test_password',
        'use_tls': False,
        'track_opens': True
    },
    'server': {
        'host': 'localhost',
        'port': 8000,
        'base_url': 'http://localhost:8000'
    },
    'database': {
        'url': 'https://test.supabase.co',
        'key': 'test_key'
    }
}

# Mock database for testing
MOCK_DB = {
    'email_accounts': [
        {
            'id': '********-0000-0000-0000-********0001',
            'email': '<EMAIL>',
            'display_name': 'Test User',
            'smtp_server': 'smtp.zoho.eu',
            'smtp_port': 465,
            'use_ssl': True,
            'daily_limit': 10,
            'active': True
        }
    ],
    'recipients': [
        {
            'id': '********-0000-0000-0000-********0002',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Recipient',
            'company': 'Test Company',
            'status': 'active'
        }
    ],
    'email_queue': [
        {
            'id': '********-0000-0000-0000-********0003',
            'account_id': '********-0000-0000-0000-********0001',
            'recipient_id': '********-0000-0000-0000-********0002',
            'subject': 'Test Email',
            'body': '<html><body><p>Test email body</p></body></html>',
            'tracking_id': '********-0000-0000-0000-************',
            'scheduled_time': '2023-01-01T12:00:00Z',
            'status': 'scheduled',
            'attempts': 0,
            'max_attempts': 3,
            'is_follow_up': False,
            'follow_up': 0
        }
    ],
    'email_tracking': [],
    'link_tracking': [],
    'server_status': []
}

# Create a base test case class that all test cases can inherit from
class BaseTestCase(unittest.TestCase):
    """Base test case for Cold Email Server tests."""
    
    def setUp(self):
        """Set up test environment."""
        # Create patches for environment variables
        self.env_patcher = patch.dict('os.environ', {
            'ZOHO_EMAIL': TEST_CONFIG['email']['sender_email'],
            'ZOHO_APP_PASSWORD': TEST_CONFIG['email']['app_password'],
            'SUPABASE_URL': TEST_CONFIG['database']['url'],
            'SUPABASE_KEY': TEST_CONFIG['database']['key']
        })
        self.env_patcher.start()
        
        # Create a mock for the Supabase client
        self.supabase_patcher = patch('supabase.create_client')
        self.mock_supabase = self.supabase_patcher.start()
        self.mock_client = MagicMock()
        self.mock_supabase.return_value = self.mock_client
        
        # Create a mock for SMTP
        self.smtp_patcher = patch('smtplib.SMTP_SSL')
        self.mock_smtp = self.smtp_patcher.start()
        self.mock_server = MagicMock()
        self.mock_smtp.return_value = self.mock_server
    
    def tearDown(self):
        """Clean up after tests."""
        self.env_patcher.stop()
        self.supabase_patcher.stop()
        self.smtp_patcher.stop()
