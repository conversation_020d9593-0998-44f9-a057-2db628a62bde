#!/usr/bin/env python3
"""
Email queue management for the Cold Email Server
"""
import threading
import time
import datetime
import queue
from typing import Dict, Any, List, Tuple

from app.config import SERVER_CONFIG, EMAIL_CONFIG
from app.utils.logger import setup_logger
from app.database.supabase_client import db
from app.email.sender import send_email

# Set up logger
logger = setup_logger('server.queue')

# Create a priority queue for scheduled emails
# Items in the queue will be tuples of (scheduled_time, email_data)
email_queue = queue.PriorityQueue()

# Lock for thread-safe operations on the queue
queue_lock = threading.Lock()

# Flag to indicate if the server is shutting down
is_shutting_down = False

# Timer for the next fetch
fetch_timer = None

def send_single_email(email: Dict[str, Any]) -> bool:
    """
    Send a single email and handle follow-up scheduling.

    Args:
        email (dict): Email data to send

    Returns:
        bool: True if successful, False otherwise
    """
    email_id = email.get('id')
    recipient_email = email.get('recipient_email')
    subject = email.get('subject')
    body = email.get('body')
    follow_up_num = email.get('follow_up', 0)
    account_id = email.get('account_id')

    # Add special logging for the last follow-up (5th follow-up)
    if follow_up_num == 5:
        logger.info(f"LAST FOLLOW-UP: Sending final (5th) follow-up email {email_id} to {recipient_email}")
        logger.info(f"LAST FOLLOW-UP: Subject: {subject}")
        logger.info(f"LAST FOLLOW-UP: No further follow-ups will be scheduled for this email thread")
    else:
        logger.info(f"Sending email {email_id} to {recipient_email} (Follow-up: {follow_up_num})")

    # Update status to sending
    db.update_email_status(email_id, 'sending')

    try:
        # Get account configuration for this email
        account_config = None
        if account_id:
            try:
                # Get account data from database
                account_data = db.get_email_account_by_id(account_id)
                if account_data:
                    # Import here to avoid circular imports
                    from app.config import get_account_config
                    account_config = get_account_config(account_data)
                    logger.info(f"Using account {account_data.get('email')} for email {email_id}")
                else:
                    logger.warning(f"Could not find account data for account_id: {account_id}")
            except Exception as e:
                logger.error(f"Error getting account configuration: {str(e)}")
                logger.warning("Falling back to global email configuration")

        # Send the email
        success, result = send_email(
            recipient_email,
            subject,
            body,
            email_id=email_id,
            account_config=account_config
        )

        if success:
            # Update status to sent
            db.update_email_status(email_id, 'sent')
            logger.info(f"Email {email_id} sent successfully to {recipient_email}")

            # Schedule follow-up if needed and if follow-ups are enabled
            if follow_up_num < 5 and EMAIL_CONFIG['send_follow_ups']:  # Only schedule up to 5 follow-ups
                # Determine follow-up schedule based on test mode
                if SERVER_CONFIG['test_mode']:
                    # In test mode, use 1-minute intervals for quick testing
                    follow_up_days = 0
                    follow_up_minutes = 1
                    logger.info("Test mode: Using 1-minute follow-up intervals")
                else:
                    # In normal mode, use the standard follow-up schedule
                    follow_up_schedule = {
                        0: 2,   # 1st follow-up: 2 days after initial email
                        1: 4,   # 2nd follow-up: 4 days after 1st follow-up
                        2: 7,   # 3rd follow-up: 7 days after 2nd follow-up
                        3: 14,  # 4th follow-up: 14 days after 3rd follow-up
                        4: 30   # 5th follow-up: 30 days after 4th follow-up
                    }
                    follow_up_days = follow_up_schedule.get(follow_up_num, 2)  # Default to 2 days if not found
                    follow_up_minutes = 0

                # Schedule the follow-up
                # If in test mode with minutes, we need to handle it differently
                if SERVER_CONFIG['test_mode'] and follow_up_minutes > 0:
                    # Get the email we just sent
                    sent_email = db.get_email_by_id(email_id)
                    if sent_email and sent_email.get('sent_time'):
                        # Calculate the scheduled time for the follow-up (1 minute from now)
                        now = datetime.datetime.now(datetime.timezone.utc)
                        follow_up_time = now + datetime.timedelta(minutes=follow_up_minutes)

                        # Get the template for this follow-up
                        next_follow_up_num = follow_up_num + 1
                        template_id = db.get_template_id(next_follow_up_num)

                        # Create a follow-up email using the template
                        follow_up_data = {
                            'account_id': sent_email.get('account_id'),
                            'recipient_id': sent_email.get('recipient_id'),
                            'content_id': template_id,
                            'scheduled_time': follow_up_time.isoformat(),
                            'status': 'scheduled',
                            'is_follow_up': True,
                            'follow_up': next_follow_up_num,
                            'previous_email_id': email_id
                        }

                        response = db.client.table('email_queue').insert(follow_up_data).execute()
                        result = db._handle_response(response)

                        if result and len(result) > 0:
                            follow_up_id = result[0]['id']
                            logger.info(f"Created test follow-up email with ID: {follow_up_id}")
                            logger.info(f"Test follow-up scheduled for: {follow_up_time.strftime('%Y-%m-%d %H:%M:%S')}")
                        else:
                            logger.error("Failed to create test follow-up email")
                else:
                    # Normal follow-up scheduling using days
                    db.schedule_follow_up(email_id, follow_up_days=follow_up_days)

                logger.info(f"Scheduled follow-up for email {email_id} in {follow_up_days} days and {follow_up_minutes} minutes")
            elif follow_up_num < 5 and not EMAIL_CONFIG['send_follow_ups']:
                logger.info(f"Follow-ups are disabled. Not scheduling follow-up for email {email_id}")

            return True
        else:
            # Update status to failed
            db.update_email_status(email_id, 'failed', result)
            logger.error(f"Failed to send email {email_id} to {recipient_email}: {result}")
            return False

    except Exception as e:
        # Update status to failed
        db.update_email_status(email_id, 'failed', str(e))
        logger.error(f"Error sending email {email_id} to {recipient_email}: {str(e)}")
        return False

def process_scheduled_emails():
    """
    Process scheduled emails from the priority queue.
    This function is run in a separate thread.
    """
    logger.info("Starting email processor thread")

    while True:
        try:
            # Check if there are any emails in the queue
            if email_queue.empty():
                # Sleep for a short time before checking again
                time.sleep(1)
                continue

            # Get the current time
            now = time.time()

            # Peek at the next email in the queue
            with queue_lock:
                if email_queue.empty():
                    continue

                # Get the next email without removing it from the queue
                priority, email = email_queue.queue[0]

            # Check if it's time to send the email
            if priority <= now:
                # Remove the email from the queue
                with queue_lock:
                    priority, email = email_queue.get()

                # Send the email
                send_single_email(email)

            else:
                # Sleep until it's time to send the next email
                sleep_time = min(priority - now, 1)  # Sleep at most 1 second
                time.sleep(max(sleep_time, 0.1))  # Sleep at least 0.1 seconds

        except Exception as e:
            logger.error(f"Error in email processor thread: {str(e)}")
            time.sleep(1)  # Sleep for a short time before trying again

def fetch_scheduled_emails(interval_minutes=60, look_ahead_minutes=60):
    """
    Fetch scheduled emails from the database and add them to the priority queue.
    This function is run periodically (every {interval_minutes} minutes) to refresh the queue.

    Args:
        interval_minutes (int): How often this function is called (in minutes) - used for logging only
        look_ahead_minutes (int): How far ahead to look for scheduled emails (in minutes)
    """
    global fetch_timer
    logger.info(f"Fetching scheduled emails from database (interval={interval_minutes}min, look_ahead={look_ahead_minutes}min)...")

    try:
        # Adjust interval for test mode
        if SERVER_CONFIG['test_mode']:
            # In test mode, use a shorter interval (20 seconds)
            actual_interval = 20 / 60  # 20 seconds in minutes
            logger.info(f"Test mode: Using shorter interval of {actual_interval:.2f} minutes (20 seconds)")
        else:
            actual_interval = interval_minutes

        # Always look ahead a fixed amount of time (default 60 minutes)
        # This ensures we always get all upcoming emails regardless of the fetch interval
        emails_to_send = db.get_emails_to_send(time_window_minutes=look_ahead_minutes)

        if not emails_to_send:
            logger.info(f"No emails scheduled for the next {look_ahead_minutes} minutes")

            # Schedule the next fetch
            threading.Timer(actual_interval * 60, fetch_scheduled_emails,
                           [interval_minutes, look_ahead_minutes]).start()
            return

        logger.info(f"Found {len(emails_to_send)} scheduled emails")

        # Add emails to the priority queue
        with queue_lock:
            # Clear the queue first to avoid duplicates
            while not email_queue.empty():
                email_queue.get()

            # Add each email to the queue with scheduled_time as priority
            for email in emails_to_send:
                scheduled_time_str = email.get('scheduled_time')
                if not scheduled_time_str:
                    logger.warning(f"Email {email.get('id')} has no scheduled time, skipping")
                    continue

                # Check if this is a 5th follow-up email
                follow_up_num = email.get('follow_up', 0)
                if follow_up_num == 5:
                    email_id = email.get('id')
                    recipient_email = email.get('recipient_email')
                    subject = email.get('subject')
                    logger.info(f"LAST FOLLOW-UP QUEUED: Found final (5th) follow-up email {email_id} in queue")
                    logger.info(f"LAST FOLLOW-UP QUEUED: Will be sent to {recipient_email}")
                    logger.info(f"LAST FOLLOW-UP QUEUED: Subject: {subject}")
                    logger.info(f"LAST FOLLOW-UP QUEUED: This is the last email in the sequence")

                try:
                    # Parse the scheduled time
                    # Handle ISO format strings with too many decimal places in microseconds
                    # First, standardize the format by ensuring it has a timezone
                    if 'Z' in scheduled_time_str:
                        scheduled_time_str = scheduled_time_str.replace('Z', '+00:00')
                    elif '+' not in scheduled_time_str and '-' not in scheduled_time_str[-6:]:
                        # No timezone specified, assume UTC
                        scheduled_time_str = scheduled_time_str + '+00:00'

                    # Fix microseconds format if needed
                    # The issue is with timestamps that have more than 6 digits for microseconds
                    if '.' in scheduled_time_str:
                        parts = scheduled_time_str.split('.')
                        if len(parts) == 2:
                            # Split the second part at the timezone marker
                            micro_and_tz = parts[1]
                            tz_marker_pos = -6  # Default position for +00:00 format

                            # Find the position of the timezone marker
                            for i, char in enumerate(micro_and_tz):
                                if char in ['+', '-']:
                                    tz_marker_pos = i
                                    break

                            # Extract microseconds and timezone
                            micro = micro_and_tz[:tz_marker_pos]
                            tz = micro_and_tz[tz_marker_pos:]

                            # Truncate microseconds to 6 digits if longer
                            if len(micro) > 6:
                                micro = micro[:6]

                            # Reconstruct the timestamp
                            scheduled_time_str = f"{parts[0]}.{micro}{tz}"

                    # Now parse the fixed timestamp
                    scheduled_time = datetime.datetime.fromisoformat(scheduled_time_str)

                    # Convert to timestamp for priority queue (lower value = higher priority)
                    priority = scheduled_time.timestamp()

                    # Add to queue
                    email_queue.put((priority, email))
                    logger.debug(f"Added email {email.get('id')} to queue with priority {priority} ({scheduled_time})")
                except (ValueError, AttributeError) as e:
                    logger.error(f"Error parsing scheduled time for email {email.get('id')}: {str(e)}")
                    # Try a fallback method for parsing
                    try:
                        # Use a more lenient parsing approach
                        import dateutil.parser
                        scheduled_time = dateutil.parser.parse(scheduled_time_str)
                        priority = scheduled_time.timestamp()
                        email_queue.put((priority, email))
                        logger.info(f"Successfully parsed time using fallback method for email {email.get('id')}: {scheduled_time}")
                    except Exception as e2:
                        logger.error(f"Fallback parsing also failed for email {email.get('id')}: {str(e2)}")
                        # As a last resort, use current time + 1 hour to ensure it gets processed
                        scheduled_time = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1)
                        priority = scheduled_time.timestamp()
                        email_queue.put((priority, email))
                        logger.warning(f"Using default time for email {email.get('id')}: {scheduled_time}")

        logger.info(f"Added {len(emails_to_send)} emails to the priority queue")

        # Schedule the next fetch if not shutting down
        if not is_shutting_down:
            fetch_timer = threading.Timer(actual_interval * 60, fetch_scheduled_emails,
                                         [interval_minutes, look_ahead_minutes])
            fetch_timer.daemon = True
            fetch_timer.start()

    except Exception as e:
        logger.error(f"Error fetching scheduled emails: {str(e)}")

        # Schedule the next fetch even if there was an error, but only if not shutting down
        if not is_shutting_down:
            fetch_timer = threading.Timer(interval_minutes * 60, fetch_scheduled_emails,
                                         [interval_minutes, look_ahead_minutes])
            fetch_timer.daemon = True
            fetch_timer.start()

def start_email_processor(interval_minutes=2, look_ahead_minutes=60):
    """
    Start the email processor thread and schedule the first fetch.

    Args:
        interval_minutes (int): How often to fetch emails from the database (in minutes)
        look_ahead_minutes (int): How far ahead to look for scheduled emails (in minutes)
    """
    global is_shutting_down
    is_shutting_down = False

    # Start the email processor thread
    processor_thread = threading.Thread(target=process_scheduled_emails, daemon=True)
    processor_thread.start()
    logger.info("Email processor thread started")

    # Schedule the first fetch
    fetch_scheduled_emails(interval_minutes, look_ahead_minutes)

def stop_email_processor():
    """
    Stop the email processor by canceling any scheduled fetches and setting the shutdown flag.
    This should be called when the server is shutting down.
    """
    global is_shutting_down, fetch_timer

    logger.info("Stopping email processor...")

    # Set the shutdown flag to prevent new timers from being created
    is_shutting_down = True

    # Cancel the current fetch timer if it exists
    if fetch_timer is not None and fetch_timer.is_alive():
        logger.info("Canceling scheduled fetch timer")
        fetch_timer.cancel()

    logger.info("Email processor stopped")

def setup_test_email():
    """
    Set up a test email in test mode.
    Creates a new email in the queue scheduled for 1 minute from now.
    """
    if not SERVER_CONFIG['test_mode']:
        return

    logger.info("Setting up test email for test mode...")

    try:
        # Get a test recipient (create if doesn't exist)
        test_email = SERVER_CONFIG['test_recipient_email']
        recipient_response = db.client.table('recipients').select('*').eq('email', test_email).execute()
        recipient_data = db._handle_response(recipient_response)

        if not recipient_data or len(recipient_data) == 0:
            # Create a new recipient
            logger.info(f"Creating test recipient: {test_email}")
            recipient_data = db.create_recipient(
                email=test_email,
                first_name="Test",
                last_name="User",
                company="Test Company"
            )
            if not recipient_data or len(recipient_data) == 0:
                logger.error("Failed to create test recipient")
                return
            recipient_id = recipient_data[0]['id']
        else:
            recipient_id = recipient_data[0]['id']
            logger.info(f"Using existing test recipient: {test_email} (ID: {recipient_id})")

        # Get an active email account
        account_response = db.client.table('email_accounts').select('*').eq('active', True).execute()
        account_data = db._handle_response(account_response)

        if not account_data or len(account_data) == 0:
            logger.error("No active email accounts found")
            return

        account_id = account_data[0]['id']
        logger.info(f"Using email account ID: {account_id}")

        # Schedule the email for 1 minute from now
        now = datetime.datetime.now(datetime.timezone.utc)
        scheduled_time = now + datetime.timedelta(minutes=1)

        # Get the template for the initial email (follow_up = 0)
        template_id = db.get_template_id(0)

        # Get the template content for logging
        response = db.client.table('email_content').select('*').eq('id', template_id).execute()
        template = db._handle_response(response)

        if not template or len(template) == 0:
            logger.error(f"Template with ID {template_id} not found")
            return

        subject = template[0]['subject']
        logger.info(f"Using template {template_id} with subject: {subject}")

        # Add the email to the queue
        email_data = {
            'account_id': account_id,
            'recipient_id': recipient_id,
            'content_id': template_id,
            'scheduled_time': scheduled_time.isoformat(),
            'status': 'scheduled',
            'follow_up': 0
        }

        response = db.client.table('email_queue').insert(email_data).execute()
        result = db._handle_response(response)

        if result and len(result) > 0:
            email_id = result[0]['id']
            logger.info(f"Created test email with ID: {email_id}")
            logger.info(f"Test email scheduled for: {scheduled_time.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            logger.error("Failed to create test email")

    except Exception as e:
        logger.error(f"Error setting up test email: {str(e)}")