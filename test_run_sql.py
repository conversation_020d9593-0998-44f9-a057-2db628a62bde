#!/usr/bin/env python3
"""
Test script to run the SQL setup script on the Supabase database
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(override=True)

# Add the current directory to the path so we can import the app modules
sys.path.append('.')

from app.utils.logger import setup_logger
import supabase
import requests

# Set up logger
logger = setup_logger()

def main():
    """Main function to run the SQL setup script on Supabase."""
    # Get Supabase credentials from environment
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    
    print("Supabase configuration:")
    print(f"URL: {supabase_url}")
    print(f"Key: {supabase_key[:10]}... (truncated)")
    
    # Read the SQL setup script
    print("\nReading SQL setup script...")
    with open('supabase_setup.sql', 'r') as f:
        sql_script = f.read()
    
    print(f"SQL script length: {len(sql_script)} characters")
    
    # Split the script into individual statements
    print("\nSplitting SQL script into statements...")
    statements = sql_script.split(';')
    print(f"Found {len(statements)} statements")
    
    # Create Supabase client
    print("\nConnecting to Supabase...")
    client = supabase.create_client(supabase_url, supabase_key)
    
    # Try to run each statement
    print("\nRunning SQL statements...")
    success_count = 0
    error_count = 0
    
    for i, statement in enumerate(statements):
        # Skip empty statements
        if not statement.strip():
            continue
        
        # Add back the semicolon
        statement = statement.strip() + ';'
        
        # Print a preview of the statement
        preview = statement[:50].replace('\n', ' ').strip()
        if len(statement) > 50:
            preview += '...'
        
        print(f"\nStatement {i+1}: {preview}")
        
        try:
            # Use the REST API to run the SQL statement
            headers = {
                'apikey': supabase_key,
                'Authorization': f'Bearer {supabase_key}',
                'Content-Type': 'application/json',
                'Prefer': 'params=single-object'
            }
            
            data = {
                'query': statement
            }
            
            response = requests.post(
                f"{supabase_url}/rest/v1/rpc/execute_sql",
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                print(f"Success: {response.text[:100]}")
                success_count += 1
            else:
                print(f"Error: {response.status_code} - {response.text}")
                error_count += 1
        except Exception as e:
            print(f"Exception: {str(e)}")
            error_count += 1
    
    print(f"\nFinished running SQL statements. Success: {success_count}, Errors: {error_count}")

if __name__ == "__main__":
    main()
