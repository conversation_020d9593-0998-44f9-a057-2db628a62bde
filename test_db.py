#!/usr/bin/env python3
"""
Test script to retrieve recipients from the database
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(override=True)

# Add the current directory to the path so we can import the app modules
sys.path.append('.')

from app.database.supabase_client import SupabaseClient
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger()

def main():
    """Main function to test database operations."""
    # Print Supabase configuration
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')

    print("Supabase configuration:")
    print(f"URL: {supabase_url}")
    print(f"Key: {supabase_key[:10]}... (truncated)")

    # Create a new Supabase client
    db = SupabaseClient(url=supabase_url, key=supabase_key)

    # Check if connected
    if db.is_connected():
        print("\nConnected to Supabase!")
    else:
        print("\nNot connected to Supabase. Using mock database.")

    # Get recipients
    try:
        # Try to get recipients directly from the table
        if db.is_connected():
            response = db.client.table('recipients').select('*').execute()
            recipients = response.data
        else:
            recipients = db._mock_db['recipients']

        print(f"\nFound {len(recipients)} recipients:")
        for r in recipients:
            print(f"- {r.get('email')}, ID: {r.get('id')}")
    except Exception as e:
        print(f"Error getting recipients: {str(e)}")

    # If no recipients, create a test recipient
    if not recipients:
        print("\nNo recipients found. Creating a test recipient...")
        new_recipient = db.create_recipient(
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            company="Test Company"
        )
        print(f"Created recipient: {new_recipient.get('email')}, ID: {new_recipient.get('id')}")

    # Get email accounts
    accounts = db.get_email_accounts()
    print(f"\nFound {len(accounts)} email accounts:")
    for a in accounts:
        print(f"- {a.get('email')}, ID: {a.get('id')}")

    # If no accounts, create a test account
    if not accounts:
        print("\nNo email accounts found. Creating a test account...")
        new_account = db.create_email_account(
            email="<EMAIL>",
            display_name="Mateusz",
            smtp_server="smtp.zoho.eu",
            smtp_port=465,
            use_ssl=True,
            daily_limit=10
        )
        print(f"Created account: {new_account.get('email')}, ID: {new_account.get('id')}")

if __name__ == "__main__":
    main()
