#!/usr/bin/env python3
"""
Email quota management system for production email scheduling.
Handles daily limits, quota allocation, and postponement logic.
"""
import datetime
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from app.config import SCHEDULING_CONFIG
from app.utils.logger import setup_logger

logger = setup_logger('quota_manager')

@dataclass
class EmailQuota:
    """Represents daily email quota for an account."""
    account_id: str
    daily_limit: int
    followup_quota: int
    initial_quota: int
    followup_used: int = 0
    initial_used: int = 0
    
    @property
    def followup_remaining(self) -> int:
        """Remaining follow-up quota."""
        return max(0, self.followup_quota - self.followup_used)
    
    @property
    def initial_remaining(self) -> int:
        """Remaining initial email quota."""
        return max(0, self.initial_quota - self.initial_used)
    
    @property
    def total_remaining(self) -> int:
        """Total remaining quota (can use leftover follow-up quota for initial emails)."""
        return self.followup_remaining + self.initial_remaining
    
    def can_send_followup(self) -> bool:
        """Check if we can send a follow-up email."""
        return self.followup_remaining > 0
    
    def can_send_initial(self) -> bool:
        """Check if we can send an initial email (including leftover follow-up quota)."""
        return self.total_remaining > 0
    
    def allocate_followup(self) -> bool:
        """Allocate quota for a follow-up email. Returns True if successful."""
        if self.can_send_followup():
            self.followup_used += 1
            return True
        return False
    
    def allocate_initial(self) -> bool:
        """Allocate quota for an initial email. Returns True if successful."""
        if self.initial_remaining > 0:
            self.initial_used += 1
            return True
        elif self.followup_remaining > 0:
            # Use leftover follow-up quota for initial emails
            self.followup_used += 1
            return True
        return False

@dataclass
class EmailItem:
    """Represents an email to be scheduled."""
    email_id: str
    account_id: str
    scheduled_time: datetime.datetime
    is_followup: bool
    priority_score: float = 0.0
    postponed: bool = False
    original_scheduled_time: datetime.datetime = None
    
    def __post_init__(self):
        if self.original_scheduled_time is None:
            self.original_scheduled_time = self.scheduled_time

class QuotaManager:
    """Manages email quotas and scheduling logic."""
    
    def __init__(self):
        self.quotas: Dict[str, EmailQuota] = {}
        self.config = SCHEDULING_CONFIG
    
    def initialize_account_quota(self, account_id: str, daily_limit: int) -> EmailQuota:
        """Initialize quota for an account."""
        followup_quota = int(daily_limit * self.config['quota_allocation']['followup_percentage'])
        initial_quota = int(daily_limit * self.config['quota_allocation']['initial_percentage'])
        
        # Ensure we don't exceed the daily limit due to rounding
        if followup_quota + initial_quota > daily_limit:
            initial_quota = daily_limit - followup_quota
        
        quota = EmailQuota(
            account_id=account_id,
            daily_limit=daily_limit,
            followup_quota=followup_quota,
            initial_quota=initial_quota
        )
        
        self.quotas[account_id] = quota
        logger.info(f"Initialized quota for account {account_id}: "
                   f"Daily={daily_limit}, Follow-up={followup_quota}, Initial={initial_quota}")
        return quota
    
    def get_quota(self, account_id: str) -> EmailQuota:
        """Get quota for an account."""
        return self.quotas.get(account_id)
    
    def calculate_priority_score(self, email: EmailItem) -> float:
        """
        Calculate priority score for an email.
        Lower score = higher priority.
        """
        base_score = email.scheduled_time.timestamp()
        
        # Follow-ups get higher priority (lower score)
        if email.is_followup:
            base_score -= 86400  # Subtract 1 day worth of seconds
        
        # Postponed emails get lower priority (higher score)
        if email.postponed:
            base_score += 86400 * 10  # Add 10 days worth of seconds
        
        return base_score
    
    def is_within_business_hours(self, dt: datetime.datetime) -> bool:
        """Check if datetime is within business hours."""
        hour = dt.hour
        start_hour = self.config['business_hours']['start_hour']
        end_hour = self.config['business_hours']['end_hour']
        return start_hour <= hour < end_hour
    
    def adjust_to_business_hours(self, dt: datetime.datetime) -> datetime.datetime:
        """Adjust datetime to fall within business hours."""
        start_hour = self.config['business_hours']['start_hour']
        end_hour = self.config['business_hours']['end_hour']
        
        # If before business hours, move to start of business day
        if dt.hour < start_hour:
            return dt.replace(hour=start_hour, minute=0, second=0, microsecond=0)
        
        # If after business hours, move to start of next business day
        if dt.hour >= end_hour:
            next_day = dt + datetime.timedelta(days=1)
            return next_day.replace(hour=start_hour, minute=0, second=0, microsecond=0)
        
        return dt
    
    def postpone_email(self, email: EmailItem) -> EmailItem:
        """Postpone an email by the configured number of days."""
        days_to_postpone = self.config['postponement']['days_to_postpone']
        new_scheduled_time = email.scheduled_time + datetime.timedelta(days=days_to_postpone)
        
        # Ensure the new time is within business hours
        new_scheduled_time = self.adjust_to_business_hours(new_scheduled_time)
        
        email.scheduled_time = new_scheduled_time
        email.postponed = True
        
        logger.info(f"Postponed email {email.email_id} by {days_to_postpone} day(s) "
                   f"to {new_scheduled_time}")
        
        return email
