#!/usr/bin/env python3
"""
Update database functions to use the email_content table
"""
import sys
from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('update_script')

def test_get_emails_to_send():
    """
    Test the get_emails_to_send function
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("Testing get_emails_to_send function...")
        response = db.client.rpc(
            'get_emails_to_send',
            {'time_window_minutes': 60}
        ).execute()
        
        if response.error:
            logger.error(f"Error calling get_emails_to_send: {response.error}")
            return False
        
        result = db._handle_response(response)
        logger.info(f"get_emails_to_send returned {len(result) if result else 0} emails")
        return True
    except Exception as e:
        logger.error(f"Error testing get_emails_to_send: {str(e)}")
        return False

def main():
    """Main entry point."""
    logger.info("Starting database function update...")
    
    # Test the get_emails_to_send function
    if test_get_emails_to_send():
        logger.info("get_emails_to_send function is working correctly")
        return 0
    else:
        logger.error("get_emails_to_send function is not working correctly")
        logger.info("Please execute the SQL updates manually in the Supabase dashboard")
        
        # Print the SQL updates
        with open('update_get_emails_to_send.sql', 'r') as f:
            get_emails_sql = f.read()
        
        with open('update_schedule_follow_up_email.sql', 'r') as f:
            schedule_follow_up_sql = f.read()
        
        print("\n--- SQL to update get_emails_to_send ---\n")
        print(get_emails_sql)
        print("\n--- SQL to update schedule_follow_up_email ---\n")
        print(schedule_follow_up_sql)
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
