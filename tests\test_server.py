#!/usr/bin/env python3
"""
Tests for the server module
"""
import unittest
from unittest.mock import patch, MagicMock, call
import os
import sys
import json
import http.server
import socketserver
import threading
import io

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.server import EmailHandler, run_server, process_scheduled_emails, process_follow_ups

class MockRequest:
    """Mock request object for testing the EmailHandler."""

    def __init__(self, method='GET', path='/'):
        self.method = method
        self.path = path
        self.headers = {}
        self.rfile = io.BytesIO()
        self.wfile = io.BytesIO()
        self.client_address = ('127.0.0.1', 12345)

    def makefile(self, *args, **kwargs):
        """Mock makefile method."""
        return self.rfile

class TestServer(BaseTestCase):
    """Test cases for the server module."""

    def setUp(self):
        """Set up test environment."""
        super().setUp()

        # Mock socketserver.TCPServer
        self.tcp_server_patcher = patch('socketserver.TCPServer')
        self.mock_tcp_server = self.tcp_server_patcher.start()
        self.mock_server_instance = MagicMock()
        self.mock_tcp_server.return_value.__enter__.return_value = self.mock_server_instance

        # Mock threading.Thread
        self.thread_patcher = patch('threading.Thread')
        self.mock_thread = self.thread_patcher.start()
        self.mock_thread_instance = MagicMock()
        self.mock_thread.return_value = self.mock_thread_instance

        # Mock start_ping_pong_cycle
        self.ping_pong_patcher = patch('app.utils.keep_alive.start_ping_pong_cycle')
        self.mock_ping_pong = self.ping_pong_patcher.start()

        # Mock send_email
        self.send_email_patcher = patch('app.email.sender.send_email')
        self.mock_send_email = self.send_email_patcher.start()
        self.mock_send_email.return_value = (True, '00000000-0000-0000-0000-000000000013')

        # Mock handle_tracking_pixel
        self.tracking_pixel_patcher = patch('app.tracking.handlers.handle_tracking_pixel')
        self.mock_tracking_pixel = self.tracking_pixel_patcher.start()
        self.mock_tracking_pixel.return_value = (200, 'image/gif', b'GIF89a\x01\x00\x01\x00\x80\x00\x00\xff\xff\xff\x00\x00\x00!\xf9\x04\x01\x00\x00\x00\x00,\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02D\x01\x00;')

        # Mock handle_tracking_stats
        self.tracking_stats_patcher = patch('app.tracking.handlers.handle_tracking_stats')
        self.mock_tracking_stats = self.tracking_stats_patcher.start()
        self.mock_tracking_stats.return_value = (200, 'application/json', json.dumps({'status': 'success'}).encode('utf-8'))

        # Mock handle_ping and handle_pong
        self.ping_patcher = patch('app.utils.keep_alive.handle_ping')
        self.mock_ping = self.ping_patcher.start()
        self.mock_ping.return_value = {'status': 'ping received'}

        self.pong_patcher = patch('app.utils.keep_alive.handle_pong')
        self.mock_pong = self.pong_patcher.start()
        self.mock_pong.return_value = {'status': 'pong received'}

        # Mock FollowUpManager
        self.follow_up_patcher = patch('app.email.follow_up.FollowUpManager')
        self.mock_follow_up = self.follow_up_patcher.start()
        self.mock_follow_up_instance = MagicMock()
        self.mock_follow_up.return_value = self.mock_follow_up_instance
        self.mock_follow_up_instance.process_follow_ups.return_value = []
        self.mock_follow_up_instance.schedule_follow_up.return_value = '00000000-0000-0000-0000-000000000014'

        # Mock db.get_emails_to_send
        self.get_emails_patcher = patch('app.database.supabase_client.db.get_emails_to_send')
        self.mock_get_emails = self.get_emails_patcher.start()
        self.mock_get_emails.return_value = []

    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        self.tcp_server_patcher.stop()
        self.thread_patcher.stop()
        self.ping_pong_patcher.stop()
        self.send_email_patcher.stop()
        self.tracking_pixel_patcher.stop()
        self.tracking_stats_patcher.stop()
        self.ping_patcher.stop()
        self.pong_patcher.stop()
        self.follow_up_patcher.stop()
        self.get_emails_patcher.stop()

    def test_run_server(self):
        """Test running the server."""
        # Call the function
        run_server(
            host='localhost',
            port=8000,
            keep_alive=True,
            process_follow_ups_enabled=True,
            process_emails_enabled=True
        )

        # Check that the server was started
        self.mock_tcp_server.assert_called_once_with(('localhost', 8000), EmailHandler)

        # Check that the keep-alive mechanism was started
        self.mock_ping_pong.assert_called_once()

        # Check that the follow-up processor was started
        self.mock_thread.assert_called()
        self.mock_thread_instance.start.assert_called()

    def test_process_scheduled_emails(self):
        """Test processing scheduled emails."""
        # Set up mock emails
        self.mock_get_emails.return_value = [
            {
                'id': '00000000-0000-0000-0000-000000000003',
                'recipient_email': '<EMAIL>',
                'subject': 'Test Email',
                'body': '<html><body><p>Test body</p></body></html>',
                'scheduled_time': '2021-01-01T00:00:00Z',
                'is_follow_up': False,
                'follow_up': 0
            }
        ]

        # Create a mock for update_email_status
        with patch('app.database.supabase_client.db.update_email_status') as mock_update_status:
            # Call the function
            process_scheduled_emails()

            # Check that get_emails_to_send was called
            self.mock_get_emails.assert_called_once()

            # Check that send_email was called for each email
            self.mock_send_email.assert_called_once_with(
                '<EMAIL>',
                'Test Email',
                '<html><body><p>Test body</p></body></html>',
                email_id='00000000-0000-0000-0000-000000000003'
            )

            # Check that update_email_status was called
            mock_update_status.assert_called()

    def test_process_follow_ups(self):
        """Test processing follow-ups."""
        # Call the function
        process_follow_ups()

        # Check that FollowUpManager was created
        self.mock_follow_up.assert_called_once()

        # Check that process_follow_ups was called
        self.mock_follow_up_instance.process_follow_ups.assert_called_once()

    def test_email_handler_get_tracking_pixel(self):
        """Test EmailHandler handling a tracking pixel request."""
        # Create a mock request
        request = MockRequest(method='GET', path='/track/00000000-0000-0000-0000-000000000013/transparent.gif')

        # Create a handler
        handler = EmailHandler(request, ('127.0.0.1', 12345), None)

        # Check that handle_tracking_pixel was called
        self.mock_tracking_pixel.assert_called_once_with(
            '/track/00000000-0000-0000-0000-000000000013/transparent.gif',
            {},
            ('127.0.0.1', 12345)
        )

    def test_email_handler_get_tracking_stats(self):
        """Test EmailHandler handling a tracking stats request."""
        # Create a mock request
        request = MockRequest(method='GET', path='/tracking')

        # Create a handler
        handler = EmailHandler(request, ('127.0.0.1', 12345), None)

        # Check that handle_tracking_stats was called
        self.mock_tracking_stats.assert_called_once_with('/tracking')

    def test_email_handler_get_ping(self):
        """Test EmailHandler handling a ping request."""
        # Create a mock request
        request = MockRequest(method='GET', path='/ping')

        # Create a handler
        handler = EmailHandler(request, ('127.0.0.1', 12345), None)

        # Check that handle_ping was called
        self.mock_ping.assert_called_once()

    def test_email_handler_get_pong(self):
        """Test EmailHandler handling a pong request."""
        # Create a mock request
        request = MockRequest(method='GET', path='/pong')

        # Create a handler
        handler = EmailHandler(request, ('127.0.0.1', 12345), None)

        # Check that handle_pong was called
        self.mock_pong.assert_called_once()

    def test_email_handler_post_send_email(self):
        """Test EmailHandler handling a send-email request."""
        # Create a mock request
        request = MockRequest(method='POST', path='/send-email')
        request.rfile = io.BytesIO(json.dumps({
            'recipient': '<EMAIL>',
            'subject': 'Test Email',
            'body': '<html><body><p>Test body</p></body></html>'
        }).encode('utf-8'))
        request.headers = {'Content-Length': str(len(request.rfile.getvalue()))}

        # Create a handler
        handler = EmailHandler(request, ('127.0.0.1', 12345), None)

        # Check that send_email was called
        self.mock_send_email.assert_called_once_with(
            '<EMAIL>',
            'Test Email',
            '<html><body><p>Test body</p></body></html>',
            None
        )

if __name__ == '__main__':
    unittest.main()
