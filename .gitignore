# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# Temporary files
tmp/
temp/

# Compiled files
*.pyc
*.pyo
*.pyd

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover

# Jupyter Notebook
.ipynb_checkpoints

# Local configuration
local_settings.py
instance/
.webassets-cache

# Secrets and credentials
*.pem
*.key
credentials.json
secrets.json
.env*
!.env.example

# Project specific
.augmentignore