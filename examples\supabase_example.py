#!/usr/bin/env python3
"""
Example script demonstrating how to use the Supabase client
"""
import os
import sys
import datetime
import dotenv
from pathlib import Path

# Add the parent directory to the path so we can import the app
sys.path.insert(0, str(Path(__file__).parent.parent))

# Load environment variables from .env file
dotenv.load_dotenv()

from app.database import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger()

def main():
    """Main function."""
    # Check if Supabase is connected
    if db.is_connected():
        logger.info("Connected to Supabase")
    else:
        logger.warning("Not connected to Supabase, using mock database")

    # Create email accounts
    logger.info("Creating email accounts...")
    accounts = []
    for i in range(1, 6):
        account = db.create_email_account(
            email=f"account{i}@example.com",
            display_name=f"Account {i}",
            daily_limit=10
        )
        accounts.append(account)
        logger.info(f"Created account: {account.get('email')}")

    # Create recipients
    logger.info("Creating recipients...")
    recipients = []
    for i in range(1, 11):
        recipient = db.create_recipient(
            email=f"recipient{i}@example.com",
            first_name=f"First{i}",
            last_name=f"Last{i}",
            company=f"Company {i}"
        )
        recipients.append(recipient)
        logger.info(f"Created recipient: {recipient.get('email')}")

    # Schedule emails for each account
    logger.info("Scheduling emails...")
    today = datetime.date.today()

    for i, account in enumerate(accounts):
        # Get recipient IDs for this account (assign a subset of recipients to each account)
        start_idx = i % len(recipients)
        recipient_subset = recipients[start_idx:start_idx+3] if start_idx+3 <= len(recipients) else recipients[start_idx:] + recipients[:start_idx+3-len(recipients)]
        recipient_ids = [r.get('id') for r in recipient_subset]

        # Schedule emails for this account
        scheduled_emails = db.schedule_emails_for_account(
            account_id=account.get('id'),
            recipient_ids=recipient_ids,
            subject=f"Test Email Batch {i+1}",
            body=f"<p>This is a test email from batch {i+1}.</p>",
            date=today,
            num_emails=3  # Schedule 3 emails per account
        )

        logger.info(f"Scheduled {len(scheduled_emails)} emails for account {account.get('email')}")

        # Log the scheduled emails
        for j, email in enumerate(scheduled_emails):
            recipient_id = email.get('recipient_id')
            recipient = next((r for r in recipients if r.get('id') == recipient_id), None)
            if recipient:
                scheduled_time = datetime.datetime.fromisoformat(email.get('scheduled_time'))
                logger.info(f"  Email {j+1}: To {recipient.get('email')} at {scheduled_time.strftime('%H:%M:%S')}")

    # Get emails to send in the next hour
    logger.info("Getting emails to send in the next hour...")
    emails_to_send = db.get_emails_to_send(time_window_minutes=60)
    for email in emails_to_send:
        logger.info(f"Email to send: {email.get('subject')} to {email.get('recipient_email')}")

    # Update server status
    logger.info("Updating server status...")
    next_ping = datetime.datetime.now() + datetime.timedelta(minutes=14)
    status = db.update_server_status(
        status="running",
        next_ping=next_ping
    )
    logger.info(f"Updated server status: {status.get('status')}")

    logger.info("Example completed successfully")

if __name__ == "__main__":
    main()
