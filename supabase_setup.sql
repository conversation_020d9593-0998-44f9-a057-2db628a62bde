-- Supabase Database Setup for Cold Email Server
-- This script creates all necessary tables for a multi-user cold email system
-- with tracking capabilities

-- Drop existing tables and functions to ensure a clean slate
-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS server_status CASCADE;
DROP TABLE IF EXISTS link_tracking CASCADE;
DROP TABLE IF EXISTS email_tracking CASCADE;
DROP TABLE IF EXISTS email_queue CASCADE;
DROP TABLE IF EXISTS recipients CASCADE;
DROP TABLE IF EXISTS email_accounts CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS get_emails_to_send(INTEGER);
DROP FUNCTION IF EXISTS get_emails_needing_follow_up();
DROP FUNCTION IF EXISTS schedule_follow_up_email(U<PERSON><PERSON>, INTEGER, VARCHAR, TEXT);
DROP FUNCTION IF EXISTS generate_random_time(DATE);

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Email Accounts Table
-- Stores information about the 5 email accounts used to send emails
CREATE TABLE email_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) NOT NULL UNIQUE,
  display_name VARCHAR(255),
  smtp_server VARCHAR(255) NOT NULL DEFAULT 'smtp.zoho.eu',
  smtp_port INTEGER NOT NULL DEFAULT 465,
  use_ssl BOOLEAN NOT NULL DEFAULT TRUE,
  daily_limit INTEGER NOT NULL DEFAULT 10,
  active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Recipients Table
-- Stores information about email recipients
CREATE TABLE recipients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) NOT NULL UNIQUE,
  first_name VARCHAR(255),
  last_name VARCHAR(255),
  company VARCHAR(255),
  status VARCHAR(50) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Email Queue Table
-- Acts as a queue for emails to be sent
CREATE TABLE email_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  account_id UUID NOT NULL REFERENCES email_accounts(id),
  recipient_id UUID NOT NULL REFERENCES recipients(id),
  subject VARCHAR(255) NOT NULL,
  body TEXT NOT NULL,
  tracking_id UUID NOT NULL UNIQUE DEFAULT uuid_generate_v4(),
  scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'scheduled',
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  last_attempt_time TIMESTAMP WITH TIME ZONE,
  sent_time TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  -- Follow-up email tracking
  is_follow_up BOOLEAN DEFAULT FALSE,
  follow_up INTEGER DEFAULT 0, -- 0 for first email, 1 for 1st followup, etc.
  previous_email_id UUID REFERENCES email_queue(id),
  -- Reply tracking
  message_id VARCHAR(255), -- Message-ID header of the sent email
  reply_message_id VARCHAR(255), -- Message-ID of the reply
  reply_references VARCHAR(255), -- References header of the reply
  reply_in_reply_to VARCHAR(255), -- In-Reply-To header of the reply
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Indexes for efficient querying
  CONSTRAINT valid_status CHECK (status IN ('scheduled', 'sending', 'sent', 'failed', 'cancelled', 'replied'))
);

CREATE INDEX idx_email_queue_status ON email_queue(status);
CREATE INDEX idx_email_queue_scheduled_time ON email_queue(scheduled_time);
CREATE INDEX idx_email_queue_account_id ON email_queue(account_id);
CREATE INDEX idx_email_queue_tracking_id ON email_queue(tracking_id);
CREATE INDEX idx_email_queue_previous_email_id ON email_queue(previous_email_id);
CREATE INDEX idx_email_queue_follow_up ON email_queue(follow_up);

-- 4. Email Tracking Table
-- Stores tracking information for sent emails
CREATE TABLE email_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email_id UUID NOT NULL REFERENCES email_queue(id),
  tracking_id UUID NOT NULL REFERENCES email_queue(tracking_id),
  recipient_id UUID NOT NULL REFERENCES recipients(id),
  open_time TIMESTAMP WITH TIME ZONE, -- NULL until the email is opened
  user_agent TEXT,
  ip_address VARCHAR(45),
  device_type VARCHAR(50),
  browser VARCHAR(100),
  os VARCHAR(100),
  country VARCHAR(100),
  city VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_email_tracking_tracking_id ON email_tracking(tracking_id);
CREATE INDEX idx_email_tracking_email_id ON email_tracking(email_id);

-- Daily Schedules Table has been removed to simplify the database structure
-- Email scheduling is now handled directly through the email_queue table

-- 6. Link Tracking Table
-- Tracks clicks on links in emails
CREATE TABLE link_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email_id UUID NOT NULL REFERENCES email_queue(id),
  tracking_id UUID NOT NULL REFERENCES email_queue(tracking_id),
  recipient_id UUID NOT NULL REFERENCES recipients(id),
  link_url TEXT NOT NULL,
  link_id VARCHAR(100),
  click_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  user_agent TEXT,
  ip_address VARCHAR(45),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_link_tracking_tracking_id ON link_tracking(tracking_id);

-- 7. Server Status Table
-- Tracks server status for monitoring
CREATE TABLE server_status (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  status VARCHAR(50) NOT NULL,
  last_ping TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  next_ping TIMESTAMP WITH TIME ZONE,
  uptime_seconds INTEGER,
  version VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Functions related to daily_schedules have been removed
-- The following functions were removed:
-- 1. generate_random_times - Generated random times between 9am and 5pm
-- 2. create_daily_schedule - Created a daily schedule for an account

-- New function to generate a random time between 9am and 5pm for direct email scheduling
CREATE OR REPLACE FUNCTION generate_random_time(
  schedule_date DATE DEFAULT CURRENT_DATE
)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
DECLARE
  business_start TIME := '09:00:00';
  business_end TIME := '17:00:00';
  random_time TIMESTAMP WITH TIME ZONE;
  business_seconds INTEGER;
  random_offset INTEGER;
BEGIN
  -- Calculate business day duration in seconds
  business_seconds := EXTRACT(EPOCH FROM business_end) - EXTRACT(EPOCH FROM business_start);

  -- Generate random offset within business hours
  random_offset := floor(random() * business_seconds);

  -- Create timestamp with random offset
  random_time := (schedule_date + business_start) + (random_offset || ' seconds')::INTERVAL;

  RETURN random_time;
END;
$$ LANGUAGE plpgsql;

-- Function to schedule a follow-up email
CREATE OR REPLACE FUNCTION schedule_follow_up_email(
  email_id UUID,
  follow_up_days INTEGER,
  follow_up_subject VARCHAR(255) DEFAULT NULL,
  follow_up_body TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  previous_email RECORD;
  follow_up_id UUID;
  follow_up_num INTEGER;
  follow_up_scheduled_time TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get the previous email details
  SELECT
    eq.*,
    r.email AS recipient_email
  INTO previous_email
  FROM email_queue eq
  JOIN recipients r ON eq.recipient_id = r.id
  WHERE eq.id = email_id;

  IF previous_email IS NULL THEN
    RAISE EXCEPTION 'Email with ID % not found', email_id;
  END IF;

  -- Determine the follow-up number
  follow_up_num := previous_email.follow_up + 1;

  -- Calculate the scheduled time for the follow-up
  follow_up_scheduled_time := previous_email.sent_time + (follow_up_days || ' days')::INTERVAL;

  -- Use default subject/body if not provided
  IF follow_up_subject IS NULL THEN
    IF follow_up_num = 1 THEN
      follow_up_subject := 'Re: ' || previous_email.subject;
    ELSE
      follow_up_subject := 'Re: ' || previous_email.subject || ' (Follow-up ' || follow_up_num || ')';
    END IF;
  END IF;

  IF follow_up_body IS NULL THEN
    IF follow_up_num = 1 THEN
      follow_up_body := '<p>I wanted to follow up on my previous email. Did you have a chance to review it?</p><p>Best regards,</p>';
    ELSIF follow_up_num = 2 THEN
      follow_up_body := '<p>I''m reaching out one more time regarding my previous emails. I''d appreciate your thoughts on this matter.</p><p>Best regards,</p>';
    ELSE
      follow_up_body := '<p>This is my final follow-up on this matter. Please let me know if you''d like to discuss further.</p><p>Best regards,</p>';
    END IF;
  END IF;

  -- Insert the follow-up email into the queue
  INSERT INTO email_queue (
    account_id,
    recipient_id,
    subject,
    body,
    scheduled_time,
    is_follow_up,
    follow_up,
    previous_email_id
  ) VALUES (
    previous_email.account_id,
    previous_email.recipient_id,
    follow_up_subject,
    follow_up_body,
    follow_up_scheduled_time,
    TRUE,
    follow_up_num,
    email_id
  ) RETURNING id INTO follow_up_id;

  RETURN follow_up_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get emails that need follow-ups
-- This function is kept for backward compatibility but is no longer needed
-- with the simplified follow-up structure
CREATE OR REPLACE FUNCTION get_emails_needing_follow_up()
RETURNS TABLE (
  id UUID,
  account_id UUID,
  recipient_id UUID,
  subject VARCHAR(255),
  sent_time TIMESTAMP WITH TIME ZONE,
  follow_up INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    eq.id,
    eq.account_id,
    eq.recipient_id,
    eq.subject,
    eq.sent_time,
    eq.follow_up
  FROM email_queue eq
  WHERE eq.status = 'sent'
  AND eq.sent_time IS NOT NULL
  -- In the new structure, follow-ups are scheduled directly
  -- This function is kept for backward compatibility
  LIMIT 0; -- Return empty result set
END;
$$ LANGUAGE plpgsql;

-- Function to get next emails to send
CREATE OR REPLACE FUNCTION get_emails_to_send(
  time_window_minutes INTEGER DEFAULT 5
)
RETURNS TABLE (
  id UUID,
  account_id UUID,
  recipient_id UUID,
  recipient_email VARCHAR(255),
  subject VARCHAR(255),
  body TEXT,
  tracking_id UUID,
  scheduled_time TIMESTAMP WITH TIME ZONE,
  sender_email VARCHAR(255),
  smtp_server VARCHAR(255),
  smtp_port INTEGER,
  use_ssl BOOLEAN,
  is_follow_up BOOLEAN,
  follow_up INTEGER,
  previous_email_id UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    eq.id,
    eq.account_id,
    eq.recipient_id,
    r.email AS recipient_email,
    eq.subject,
    eq.body,
    eq.tracking_id,
    eq.scheduled_time,
    ea.email AS sender_email,
    ea.smtp_server,
    ea.smtp_port,
    ea.use_ssl,
    eq.is_follow_up,
    eq.follow_up,
    eq.previous_email_id
  FROM email_queue eq
  JOIN email_accounts ea ON eq.account_id = ea.id
  JOIN recipients r ON eq.recipient_id = r.id
  WHERE eq.status = 'scheduled'
  AND eq.scheduled_time BETWEEN NOW() AND NOW() + (time_window_minutes || ' minutes')::INTERVAL
  AND ea.active = TRUE
  ORDER BY eq.scheduled_time;
END;
$$ LANGUAGE plpgsql;

-- Insert production data
INSERT INTO email_accounts (email, display_name, daily_limit)
VALUES
  ('<EMAIL>', 'Mateusz', 10);

-- Insert production recipients
INSERT INTO recipients (email, first_name, company)
VALUES
  ('<EMAIL>', 'MatHat', 'MatHat'),
  ('<EMAIL>', 'MatHat Social', 'MatHat');

-- Insert example emails into the queue
INSERT INTO email_queue (
  id,
  account_id,
  recipient_id,
  subject,
  body,
  tracking_id,
  scheduled_time,
  status,
  is_follow_up,
  follow_up,
  previous_email_id
) VALUES
  (
    '********-1111-1111-3333-********1111',
    (SELECT id FROM email_accounts WHERE email = '<EMAIL>'),
    (SELECT id FROM recipients WHERE email = '<EMAIL>'),
    'Initial Contact - Partnership Opportunity',
    '<p>Hello MatHat,</p><p>I hope this email finds you well. I am reaching out to discuss a potential partnership opportunity between our companies.</p><p>Could we schedule a brief call next week to discuss this further?</p><p>Best regards,<br>Mateusz</p>',
    '********-2222-2222-3333-********2222',
    NOW() + INTERVAL '10 minutes',
    'scheduled',
    FALSE,
    0,
    NULL
  ),
  (
    '********-3333-3333-4444-********3333',
    (SELECT id FROM email_accounts WHERE email = '<EMAIL>'),
    (SELECT id FROM recipients WHERE email = '<EMAIL>'),
    'Social Media Collaboration Proposal',
    '<p>Hello MatHat Social Team,</p><p>I would like to propose a social media collaboration between our brands. I believe we could create some engaging content together that would benefit both our audiences.</p><p>Please let me know if you are interested in exploring this idea further.</p><p>Best regards,<br>Mateusz</p>',
    '********-4444-4444-3333-********4444',
    NOW() + INTERVAL '13 minutes',
    'scheduled',
    FALSE,
    0,
    NULL
  ),
  (
    '********-5555-5555-3333-********5555',
    (SELECT id FROM email_accounts WHERE email = '<EMAIL>'),
    (SELECT id FROM recipients WHERE email = '<EMAIL>'),
    'Re: Initial Contact - Partnership Opportunity',
    '<p>Hello MatHat,</p><p>I wanted to follow up on my previous email about the partnership opportunity. Did you have a chance to review it?</p><p>I am available for a call any day next week if that works for you.</p><p>Best regards,<br>Mateusz</p>',
    '********-6666-6666-6666-********6666',
    NOW() + INTERVAL '16 minutes',
    'scheduled',
    TRUE,
    1,
    '********-1111-1111-3333-********1111'
  );

-- Create RLS policies for security
ALTER TABLE email_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE recipients ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_tracking ENABLE ROW LEVEL SECURITY;
-- daily_schedules table has been removed
ALTER TABLE link_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE server_status ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users
CREATE POLICY "Allow full access to authenticated users"
ON email_accounts FOR ALL TO authenticated USING (true);

CREATE POLICY "Allow full access to authenticated users"
ON recipients FOR ALL TO authenticated USING (true);

CREATE POLICY "Allow full access to authenticated users"
ON email_queue FOR ALL TO authenticated USING (true);

CREATE POLICY "Allow full access to authenticated users"
ON email_tracking FOR ALL TO authenticated USING (true);

-- Policy for daily_schedules has been removed

CREATE POLICY "Allow full access to authenticated users"
ON link_tracking FOR ALL TO authenticated USING (true);

CREATE POLICY "Allow full access to authenticated users"
ON server_status FOR ALL TO authenticated USING (true);

-- Function to mark an email as replied
CREATE OR REPLACE FUNCTION mark_email_as_replied(
  p_email_id UUID,
  p_reply_message_id VARCHAR(255) DEFAULT NULL,
  p_reply_references VARCHAR(255) DEFAULT NULL,
  p_reply_in_reply_to VARCHAR(255) DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  email_exists BOOLEAN;
  follow_up_ids UUID[];
BEGIN
  -- Check if the email exists
  SELECT EXISTS(SELECT 1 FROM email_queue WHERE id = p_email_id) INTO email_exists;

  IF NOT email_exists THEN
    RAISE EXCEPTION 'Email with ID % not found', p_email_id;
  END IF;

  -- Update the email status to 'replied'
  UPDATE email_queue
  SET
    status = 'replied',
    updated_at = NOW(),
    reply_message_id = p_reply_message_id,
    reply_references = p_reply_references,
    reply_in_reply_to = p_reply_in_reply_to
  WHERE id = p_email_id;

  -- Find all follow-up emails for this email and cancel them
  WITH RECURSIVE follow_ups AS (
    -- Base case: direct follow-ups of the replied email
    SELECT id, previous_email_id
    FROM email_queue
    WHERE previous_email_id = p_email_id
      AND status = 'scheduled'

    UNION ALL

    -- Recursive case: follow-ups of follow-ups
    SELECT eq.id, eq.previous_email_id
    FROM email_queue eq
    JOIN follow_ups fu ON eq.previous_email_id = fu.id
    WHERE eq.status = 'scheduled'
  )
  SELECT array_agg(id) INTO follow_up_ids FROM follow_ups;

  -- Cancel all follow-up emails if any were found
  IF follow_up_ids IS NOT NULL AND array_length(follow_up_ids, 1) > 0 THEN
    UPDATE email_queue
    SET
      status = 'cancelled',
      updated_at = NOW(),
      error_message = 'Previous email in thread was replied to'
    WHERE id = ANY(follow_up_ids);

    RETURN TRUE;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
