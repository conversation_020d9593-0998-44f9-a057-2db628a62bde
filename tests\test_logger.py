#!/usr/bin/env python3
"""
Tests for the logger module
"""
import unittest
import logging
import os
import sys
from io import StringIO
from unittest.mock import patch

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.utils.logger import setup_logger

class TestLogger(BaseTestCase):
    """Test cases for the logger module."""
    
    def test_setup_logger_default(self):
        """Test setting up a logger with default parameters."""
        logger = setup_logger()
        
        # Check logger name
        self.assertEqual(logger.name, 'cold_email_server')
        
        # Check logger level
        self.assertEqual(logger.level, logging.INFO)
        
        # Check that the logger has handlers
        self.assertTrue(len(logger.handlers) > 0 or len(logging.getLogger().handlers) > 0)
    
    def test_setup_logger_custom(self):
        """Test setting up a logger with custom parameters."""
        logger = setup_logger('test_logger', logging.DEBUG)
        
        # Check logger name
        self.assertEqual(logger.name, 'test_logger')
        
        # Check logger level
        self.assertEqual(logger.level, logging.DEBUG)
    
    def test_logger_output_format(self):
        """Test that logger output is formatted correctly."""
        # Create a string buffer to capture log output
        log_capture = StringIO()
        handler = logging.StreamHandler(log_capture)
        
        # Set a formatter that matches the one in setup_logger
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        
        # Get a logger and add our handler
        logger = setup_logger('test_format')
        logger.addHandler(handler)
        
        # Log a test message
        test_message = "This is a test log message"
        logger.info(test_message)
        
        # Get the log output
        log_output = log_capture.getvalue()
        
        # Check that the output contains the expected parts
        self.assertIn('test_format', log_output)
        self.assertIn('INFO', log_output)
        self.assertIn(test_message, log_output)
    
    def test_multiple_loggers(self):
        """Test that multiple loggers can be created with different names."""
        logger1 = setup_logger('logger1')
        logger2 = setup_logger('logger2')
        
        # Check logger names
        self.assertEqual(logger1.name, 'logger1')
        self.assertEqual(logger2.name, 'logger2')
        
        # Check that they are different loggers
        self.assertNotEqual(logger1, logger2)

if __name__ == '__main__':
    unittest.main()
