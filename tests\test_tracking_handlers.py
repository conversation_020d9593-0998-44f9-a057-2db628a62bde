#!/usr/bin/env python3
"""
Tests for the tracking handlers module
"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import json
import base64

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.tracking.handlers import (
    handle_tracking_pixel,
    handle_tracking_stats,
    test_tracking_pixel
)
from app.tracking.pixels import TRACKING_PIXELS

class TestTrackingHandlers(BaseTestCase):
    """Test cases for the tracking handlers module."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Mock record_email_open
        self.record_open_patcher = patch('app.tracking.database.record_email_open')
        self.mock_record_open = self.record_open_patcher.start()
        self.mock_record_open.return_value = True
        
        # Mock get_email_data
        self.get_email_data_patcher = patch('app.tracking.database.get_email_data')
        self.mock_get_email_data = self.get_email_data_patcher.start()
        self.mock_get_email_data.return_value = {
            'recipient': '<EMAIL>',
            'subject': 'Test Subject',
            'sent_time': '2021-01-01T00:00:00',
            'opens': [
                {
                    'time': '2021-01-01T01:00:00',
                    'user_agent': 'Mozilla/5.0',
                    'ip_address': '127.0.0.1'
                }
            ]
        }
        
        # Mock get_tracking_summary
        self.get_summary_patcher = patch('app.tracking.database.get_tracking_summary')
        self.mock_get_summary = self.get_summary_patcher.start()
        self.mock_get_summary.return_value = {
            'total_emails': 1,
            'total_opens': 1,
            'emails': {
                '00000000-0000-0000-0000-000000000013': {
                    'recipient': '<EMAIL>',
                    'subject': 'Test Subject',
                    'sent_time': '2021-01-01T00:00:00',
                    'opens': 1
                }
            }
        }
        
        # Mock generate_test_pixels
        self.generate_pixels_patcher = patch('app.tracking.pixels.generate_test_pixels')
        self.mock_generate_pixels = self.generate_pixels_patcher.start()
        self.mock_generate_pixels.return_value = {
            'tracking_id': '00000000-0000-0000-0000-000000000013',
            'red_url': 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/pixel.gif?t=1609459200',
            'blue_url': 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/blue.gif?t=1609459200',
            'logo_url': 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/logo.gif?t=1609459200',
            'transparent_url': 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/transparent.gif?t=1609459200',
            'check_url': 'http://localhost:8000/tracking/00000000-0000-0000-0000-000000000013'
        }
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        self.record_open_patcher.stop()
        self.get_email_data_patcher.stop()
        self.get_summary_patcher.stop()
        self.generate_pixels_patcher.stop()
    
    def test_handle_tracking_pixel(self):
        """Test handling a tracking pixel request."""
        # Test with transparent pixel
        path = '/track/00000000-0000-0000-0000-000000000013/transparent.gif'
        headers = {'User-Agent': 'Mozilla/5.0'}
        client_address = ('127.0.0.1', 12345)
        
        status_code, content_type, content = handle_tracking_pixel(path, headers, client_address)
        
        # Check the response
        self.assertEqual(status_code, 200)
        self.assertEqual(content_type, 'image/gif')
        self.assertEqual(content, base64.b64decode(TRACKING_PIXELS['transparent']))
        
        # Check that record_email_open was called correctly
        self.mock_record_open.assert_called_once_with(
            '00000000-0000-0000-0000-000000000013',
            'Mozilla/5.0',
            '127.0.0.1'
        )
        
        # Test with red pixel
        self.mock_record_open.reset_mock()
        path = '/track/00000000-0000-0000-0000-000000000013/pixel.gif'
        
        status_code, content_type, content = handle_tracking_pixel(path, headers, client_address)
        
        # Check the response
        self.assertEqual(status_code, 200)
        self.assertEqual(content_type, 'image/gif')
        self.assertEqual(content, base64.b64decode(TRACKING_PIXELS['red']))
        
        # Check that record_email_open was called correctly
        self.mock_record_open.assert_called_once_with(
            '00000000-0000-0000-0000-000000000013',
            'Mozilla/5.0',
            '127.0.0.1'
        )
        
        # Test with invalid path
        self.mock_record_open.reset_mock()
        path = '/track'
        
        status_code, content_type, content = handle_tracking_pixel(path, headers, client_address)
        
        # Check the response
        self.assertEqual(status_code, 404)
        self.assertEqual(content_type, 'text/plain')
        self.assertEqual(content, b'Not Found')
        
        # Check that record_email_open was not called
        self.mock_record_open.assert_not_called()
    
    def test_handle_tracking_stats_summary(self):
        """Test handling a tracking stats request for summary."""
        path = '/tracking'
        
        status_code, content_type, content = handle_tracking_stats(path)
        
        # Check the response
        self.assertEqual(status_code, 200)
        self.assertEqual(content_type, 'application/json')
        
        # Parse the JSON content
        data = json.loads(content.decode('utf-8'))
        
        # Check the data
        self.assertEqual(data['total_emails'], 1)
        self.assertEqual(data['total_opens'], 1)
        self.assertIn('00000000-0000-0000-0000-000000000013', data['emails'])
        
        # Check that get_tracking_summary was called
        self.mock_get_summary.assert_called_once()
    
    def test_handle_tracking_stats_specific(self):
        """Test handling a tracking stats request for a specific email."""
        path = '/tracking/00000000-0000-0000-0000-000000000013'
        
        status_code, content_type, content = handle_tracking_stats(path)
        
        # Check the response
        self.assertEqual(status_code, 200)
        self.assertEqual(content_type, 'application/json')
        
        # Parse the JSON content
        data = json.loads(content.decode('utf-8'))
        
        # Check the data
        self.assertEqual(data['tracking_id'], '00000000-0000-0000-0000-000000000013')
        self.assertEqual(data['recipient'], '<EMAIL>')
        self.assertEqual(data['subject'], 'Test Subject')
        self.assertEqual(data['sent_time'], '2021-01-01T00:00:00')
        self.assertEqual(data['opens'], 1)
        self.assertEqual(len(data['open_details']), 1)
        
        # Check that get_email_data was called correctly
        self.mock_get_email_data.assert_called_once_with('00000000-0000-0000-0000-000000000013')
    
    def test_handle_tracking_stats_not_found(self):
        """Test handling a tracking stats request for a non-existent email."""
        path = '/tracking/00000000-0000-0000-0000-000000000099'
        
        # Make get_email_data return None
        self.mock_get_email_data.return_value = None
        
        status_code, content_type, content = handle_tracking_stats(path)
        
        # Check the response
        self.assertEqual(status_code, 404)
        self.assertEqual(content_type, 'application/json')
        
        # Parse the JSON content
        data = json.loads(content.decode('utf-8'))
        
        # Check the data
        self.assertIn('error', data)
        self.assertIn('00000000-0000-0000-0000-000000000099', data['error'])
        
        # Check that get_email_data was called correctly
        self.mock_get_email_data.assert_called_once_with('00000000-0000-0000-0000-000000000099')
    
    def test_test_tracking_pixel(self):
        """Test the test_tracking_pixel function."""
        # Test with default parameters
        result = test_tracking_pixel()
        
        # Check the result
        self.assertEqual(result['tracking_id'], '00000000-0000-0000-0000-000000000013')
        self.assertEqual(result['red_url'], 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/pixel.gif?t=1609459200')
        self.assertEqual(result['blue_url'], 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/blue.gif?t=1609459200')
        self.assertEqual(result['logo_url'], 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/logo.gif?t=1609459200')
        self.assertEqual(result['transparent_url'], 'http://localhost:8000/track/00000000-0000-0000-0000-000000000013/transparent.gif?t=1609459200')
        self.assertEqual(result['check_url'], 'http://localhost:8000/tracking/00000000-0000-0000-0000-000000000013')
        
        # Check that generate_test_pixels was called
        self.mock_generate_pixels.assert_called_once_with(None)
        
        # Test with custom tracking ID
        self.mock_generate_pixels.reset_mock()
        tracking_id = '00000000-0000-0000-0000-000000000014'
        result = test_tracking_pixel(tracking_id)
        
        # Check that generate_test_pixels was called with the tracking ID
        self.mock_generate_pixels.assert_called_once_with(tracking_id)

if __name__ == '__main__':
    unittest.main()
