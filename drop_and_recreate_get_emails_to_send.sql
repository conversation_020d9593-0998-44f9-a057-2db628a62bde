-- First drop the existing function
DROP FUNCTION IF EXISTS get_emails_to_send(INTEGER);

-- Then recreate the function with the updated return type
CREATE OR REPLACE FUNCTION get_emails_to_send(
  time_window_minutes INTEGER DEFAULT 5
)
RETURNS TABLE (
  id UUID,
  account_id UUID,
  recipient_id UUID,
  recipient_email VARCHAR(255),
  subject VARCHAR(255),
  body TEXT,
  tracking_id UUID,
  scheduled_time TIMESTAMP WITH TIME ZONE,
  sender_email VARCHAR(255),
  smtp_server VARCHAR(255),
  smtp_port INTEGER,
  use_ssl BOOLEAN,
  is_follow_up BOOLEAN,
  follow_up INTEGER,
  previous_email_id UUID,
  content_id UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    eq.id,
    eq.account_id,
    eq.recipient_id,
    r.email AS recipient_email,
    ec.subject,
    ec.body,
    eq.tracking_id,
    eq.scheduled_time,
    ea.email AS sender_email,
    ea.smtp_server,
    ea.smtp_port,
    ea.use_ssl,
    eq.is_follow_up,
    eq.follow_up,
    eq.previous_email_id,
    eq.content_id
  FROM email_queue eq
  JOIN email_accounts ea ON eq.account_id = ea.id
  JOIN recipients r ON eq.recipient_id = r.id
  JOIN email_content ec ON eq.content_id = ec.id
  WHERE eq.status = 'scheduled'
  -- Use explicit timezone conversion to ensure consistent behavior
  AND eq.scheduled_time <= (NOW() AT TIME ZONE 'UTC' + (time_window_minutes || ' minutes')::INTERVAL)
  AND ea.active = TRUE
  ORDER BY eq.scheduled_time;
END;
$$ LANGUAGE plpgsql;
