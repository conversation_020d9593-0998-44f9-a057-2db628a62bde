#!/usr/bin/env python3
"""
<PERSON>ript to add 5 new email accounts to the database
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv(override=True)

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('add_accounts')

def add_email_accounts():
    """Add the 5 new email accounts to the database."""
    
    # List of new email accounts to add
    new_accounts = [
        {
            'email': '<EMAIL>',
            'display_name': '<PERSON><PERSON><PERSON>',
        },
        {
            'email': '<EMAIL>',
            'display_name': '<PERSON><PERSON>',
        },
        {
            'email': '<EMAIL>',
            'display_name': '<PERSON><PERSON>',
        },
        {
            'email': '<EMAIL>',
            'display_name': '<PERSON><PERSON><PERSON>',
        },
        {
            'email': '<EMAIL>',
            'display_name': '<PERSON><PERSON><PERSON>',
        }
    ]
    
    print("Adding 5 new email accounts to the database...")
    print("=" * 50)
    
    # Common configuration for all accounts (same as existing account)
    common_config = {
        'smtp_server': 'smtp.zoho.eu',
        'smtp_port': 465,
        'use_ssl': True,
        'daily_limit': 10
    }
    
    success_count = 0
    
    for account_data in new_accounts:
        email = account_data['email']
        display_name = account_data['display_name']
        
        print(f"\nAdding account: {email}")
        
        try:
            # Check if account already exists
            existing_accounts = db.get_email_accounts(active_only=False)
            account_exists = any(acc.get('email') == email for acc in existing_accounts)
            
            if account_exists:
                print(f"  ⚠️  Account {email} already exists, skipping...")
                continue
            
            # Create the account
            result = db.create_email_account(
                email=email,
                display_name=display_name,
                smtp_server=common_config['smtp_server'],
                smtp_port=common_config['smtp_port'],
                use_ssl=common_config['use_ssl'],
                daily_limit=common_config['daily_limit']
            )
            
            if result and len(result) > 0:
                account_id = result[0].get('id')
                print(f"  ✅ Successfully created account: {email}")
                print(f"     ID: {account_id}")
                print(f"     Display Name: {display_name}")
                print(f"     SMTP Server: {common_config['smtp_server']}")
                print(f"     SMTP Port: {common_config['smtp_port']}")
                print(f"     Use SSL: {common_config['use_ssl']}")
                print(f"     Daily Limit: {common_config['daily_limit']}")
                success_count += 1
            else:
                print(f"  ❌ Failed to create account: {email}")
                
        except Exception as e:
            print(f"  ❌ Error creating account {email}: {str(e)}")
    
    print(f"\n" + "=" * 50)
    print(f"Summary: Successfully added {success_count} out of {len(new_accounts)} accounts")
    
    # Show all accounts in the database
    print(f"\nAll email accounts in database:")
    try:
        all_accounts = db.get_email_accounts(active_only=False)
        for i, account in enumerate(all_accounts, 1):
            status = "Active" if account.get('active') else "Inactive"
            print(f"  {i}. {account.get('email')} ({account.get('display_name')}) - {status}")
            print(f"     ID: {account.get('id')}")
    except Exception as e:
        print(f"  ❌ Error retrieving accounts: {str(e)}")

def show_environment_variables():
    """Show the expected environment variables for the new accounts."""
    
    new_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    print(f"\n" + "=" * 50)
    print("Environment Variables Needed:")
    print("Add these to your .env file with the actual app passwords:")
    print()
    
    for email in new_emails:
        env_var_name = email.replace('@', '_').replace('.', '_').upper() + '_APP_PASSWORD'
        print(f"# For {email}:")
        print(f"{env_var_name}=your-app-password-here")
        print()

def main():
    """Main function."""
    print("Email Accounts Setup Script")
    print("=" * 50)
    
    try:
        add_email_accounts()
        show_environment_variables()
        
        print("✅ Script completed successfully!")
        print("\nNext steps:")
        print("1. Add the app passwords to your .env file using the format shown above")
        print("2. Restart your server to load the new environment variables")
        print("3. Test sending emails from the new accounts")
        
    except Exception as e:
        print(f"❌ Script failed with error: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
