#!/usr/bin/env python3
"""
Run SQL updates directly using the Supabase client
"""
import os
import sys
import argparse
from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('update_script')

def run_sql_file(file_path):
    """
    Run a SQL file against the Supabase database.
    
    Args:
        file_path (str): Path to the SQL file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Read the SQL file
        with open(file_path, 'r') as f:
            sql = f.read()
            
        # Execute the SQL directly
        logger.info(f"Executing SQL from {file_path}...")
        
        # Extract the function name from the SQL
        if "get_emails_to_send" in sql:
            function_name = "get_emails_to_send"
        elif "schedule_follow_up_email" in sql:
            function_name = "schedule_follow_up_email"
        else:
            logger.error(f"Could not determine function name from SQL file: {file_path}")
            return False
            
        # Execute the SQL directly
        response = db.client.from_("pg_catalog.pg_proc").select("*").execute()
        
        # Check if the function exists
        logger.info(f"Checking if function {function_name} exists...")
        
        # Try to call the function to see if it works
        if function_name == "get_emails_to_send":
            test_response = db.client.rpc(
                'get_emails_to_send',
                {'time_window_minutes': 60}
            ).execute()
            
            if test_response.error:
                logger.error(f"Error calling function {function_name}: {test_response.error}")
                
                # Try to create the function
                logger.info(f"Creating function {function_name}...")
                
                # Use the raw SQL API to execute the SQL
                # This is a workaround since we can't use the RPC API to create functions
                # We'll use the REST API to execute the SQL directly
                
                # First, get the Supabase URL and key
                url = os.environ.get('SUPABASE_URL')
                key = os.environ.get('SUPABASE_KEY')
                
                if not url or not key:
                    logger.error("SUPABASE_URL or SUPABASE_KEY environment variables not set")
                    return False
                
                # Use the httpx library to make a direct request
                import httpx
                
                headers = {
                    'apikey': key,
                    'Authorization': f'Bearer {key}',
                    'Content-Type': 'application/json'
                }
                
                # Make the request
                response = httpx.post(
                    f"{url}/rest/v1/",
                    headers=headers,
                    json={'query': sql}
                )
                
                if response.status_code != 200:
                    logger.error(f"Error creating function {function_name}: {response.text}")
                    return False
                
                logger.info(f"Function {function_name} created successfully")
            else:
                logger.info(f"Function {function_name} already exists and works correctly")
        
        # For now, let's just print the SQL and ask the user to execute it manually
        logger.info(f"Please execute the following SQL manually in the Supabase dashboard:")
        print("\n" + sql + "\n")
        
        return True
        
    except Exception as e:
        logger.error(f"Error running SQL file: {str(e)}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Run SQL updates directly')
    parser.add_argument('--files', nargs='+', default=['update_get_emails_to_send.sql', 'update_schedule_follow_up_email.sql'], 
                        help='Paths to the SQL files')
    
    args = parser.parse_args()
    
    success = True
    
    for file_path in args.files:
        # Check if the file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            success = False
            continue
            
        # Run the SQL file
        if not run_sql_file(file_path):
            success = False
    
    if success:
        logger.info("All updates completed successfully")
        return 0
    else:
        logger.error("One or more updates failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
