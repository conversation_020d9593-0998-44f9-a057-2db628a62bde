#!/usr/bin/env python3
"""
Tests for the config module
"""
import unittest
from unittest.mock import patch, MagicMock
import argparse
import os
import sys

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.config import EMAIL_CONFIG, SERVER_CONFIG, update_config

class TestConfig(BaseTestCase):
    """Test cases for the config module."""
    
    def test_default_config(self):
        """Test that default configuration is loaded correctly."""
        # Test email configuration
        self.assertEqual(EMAIL_CONFIG['smtp_server'], 'smtp.zoho.eu')
        self.assertEqual(EMAIL_CONFIG['smtp_port'], 465)
        self.assertEqual(EMAIL_CONFIG['sender_email'], '<EMAIL>')
        self.assertEqual(EMAIL_CONFIG['app_password'], 'test_password')
        self.assertEqual(EMAIL_CONFIG['use_tls'], False)
        self.assertEqual(EMAIL_CONFIG['track_opens'], True)
        
        # Test server configuration
        self.assertEqual(SERVER_CONFIG['host'], 'localhost')
        self.assertEqual(SERVER_CONFIG['port'], 8000)
        self.assertTrue('base_url' in SERVER_CONFIG)
    
    def test_update_config_from_args(self):
        """Test updating configuration from command line arguments."""
        # Create mock arguments
        args = argparse.Namespace(
            password='new_password',
            email='<EMAIL>',
            smtp_server='smtp.example.com',
            smtp_port=587,
            tls=True,
            host='0.0.0.0',
            port=9000,
            base_url='https://example.com',
            no_tracking=True,
            test_auth=False,
            no_keep_alive=False,
            no_follow_ups=False,
            no_email_processor=False
        )
        
        # Update configuration
        update_config(args)
        
        # Test that configuration was updated
        self.assertEqual(EMAIL_CONFIG['app_password'], 'new_password')
        self.assertEqual(EMAIL_CONFIG['sender_email'], '<EMAIL>')
        self.assertEqual(EMAIL_CONFIG['smtp_server'], 'smtp.example.com')
        self.assertEqual(EMAIL_CONFIG['smtp_port'], 587)
        self.assertEqual(EMAIL_CONFIG['use_tls'], True)
        self.assertEqual(EMAIL_CONFIG['track_opens'], False)
        
        self.assertEqual(SERVER_CONFIG['host'], '0.0.0.0')
        self.assertEqual(SERVER_CONFIG['port'], 9000)
        self.assertEqual(SERVER_CONFIG['base_url'], 'https://example.com')
    
    def test_update_config_partial(self):
        """Test updating only some configuration values."""
        # Create mock arguments with only some values
        args = argparse.Namespace(
            password=None,
            email=None,
            smtp_server=None,
            smtp_port=None,
            tls=False,
            host=None,
            port=None,
            base_url='https://new-example.com',
            no_tracking=False,
            test_auth=False,
            no_keep_alive=False,
            no_follow_ups=False,
            no_email_processor=False
        )
        
        # Save original values
        original_password = EMAIL_CONFIG['app_password']
        original_email = EMAIL_CONFIG['sender_email']
        original_smtp_server = EMAIL_CONFIG['smtp_server']
        original_smtp_port = EMAIL_CONFIG['smtp_port']
        original_host = SERVER_CONFIG['host']
        original_port = SERVER_CONFIG['port']
        
        # Update configuration
        update_config(args)
        
        # Test that only base_url was updated
        self.assertEqual(EMAIL_CONFIG['app_password'], original_password)
        self.assertEqual(EMAIL_CONFIG['sender_email'], original_email)
        self.assertEqual(EMAIL_CONFIG['smtp_server'], original_smtp_server)
        self.assertEqual(EMAIL_CONFIG['smtp_port'], original_smtp_port)
        self.assertEqual(SERVER_CONFIG['host'], original_host)
        self.assertEqual(SERVER_CONFIG['port'], original_port)
        self.assertEqual(SERVER_CONFIG['base_url'], 'https://new-example.com')

if __name__ == '__main__':
    unittest.main()
