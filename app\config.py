#!/usr/bin/env python3
"""
Configuration settings for the Cold Email Server
"""
import os
import logging
from typing import Dict, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(override=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('cold_email_server')

# Server configuration
SERVER_CONFIG = {
    'host': 'localhost',
    'port': 8000,
    'base_url': 'https://eb18-188-146-172-43.ngrok-free.app',  # Using ngrok URL for public access
    'test_mode': False,  # Set to True to enable test mode with quick follow-ups
    'test_recipient_email': '<EMAIL>',  # Test recipient email for test mode
    'endpoints': {
        '/': 'Server status',
        '/send-email': 'Send an email (POST)',
        '/email-queue': 'Get all emails from the database queue',
        '/email-queue-status': 'Get detailed information about the queue and upcoming emails',
        '/priority-queue': 'Get the current state of the in-memory priority queue',
        '/email-tracking': 'Get email tracking data',
        '/tracking': 'View tracking statistics',
        '/track/': 'Tracking pixel endpoint',
        '/scheduler-status': 'Get production scheduler status and statistics',
        '/force-preprocessing': 'Force run daily email preprocessing'
    }
}

# Email scheduling configuration
SCHEDULING_CONFIG = {
    'business_hours': {
        'start_hour': 9,    # 9:00 AM
        'end_hour': 17,     # 5:00 PM (17:00)
        'timezone': 'UTC'   # Can be changed to local timezone
    },
    'quota_allocation': {
        'followup_percentage': 0.7,    # 70% for follow-ups
        'initial_percentage': 0.3      # 30% for initial emails
    },
    'preprocessing': {
        'run_hour': 5,      # 5:00 AM - when to run daily preprocessing
        'run_minute': 0     # 5:00 AM exactly
    },
    'postponement': {
        'days_to_postpone': 1  # Postpone by 1 day when quota exceeded
    }
}

# Email configuration
EMAIL_CONFIG = {
    # SMTP settings for sending emails
    'smtp_server': 'smtp.zoho.eu',  # Zoho Mail SMTP server (using .eu domain as in test_zoho2.py)
    'smtp_port': 465,               # Zoho Mail SMTP port for SSL
    'sender_email': os.getenv('ZOHO_EMAIL', '<EMAIL>'),  # Load from .env or use default
    'sender_name': os.getenv('SENDER_NAME', 'Mateusz'),  # Sender name for email signature
    'app_password': os.getenv('ZOHO_APP_PASSWORD', ''),  # Load from .env or use empty string
    'use_tls': False,    # Using SSL instead of TLS
    'track_opens': True,  # Set to False to disable tracking
    'send_follow_ups': True,  # Set to False to disable follow-up emails
}

def get_account_app_password(account_email: str) -> Optional[str]:
    """
    Get the app password for a specific email account from environment variables.

    Args:
        account_email (str): Email address of the account

    Returns:
        Optional[str]: App password if found, None otherwise
    """
    # Try different environment variable naming patterns
    # Pattern 1: Use the email address directly (replace @ and . with _)
    env_var_name = account_email.replace('@', '_').replace('.', '_').upper() + '_APP_PASSWORD'
    app_password = os.getenv(env_var_name)

    if app_password:
        logger.info(f"Found app password for {account_email} using env var: {env_var_name}")
        return app_password

    # Pattern 2: Try numbered accounts (ACCOUNT_1_APP_PASSWORD, etc.)
    # This requires a mapping in the database or config, but for now we'll use the email-based approach

    # Pattern 3: Fallback to the original ZOHO_APP_PASSWORD for backward compatibility
    if account_email == EMAIL_CONFIG.get('sender_email'):
        fallback_password = os.getenv('ZOHO_APP_PASSWORD')
        if fallback_password:
            logger.info(f"Using fallback ZOHO_APP_PASSWORD for {account_email}")
            return fallback_password

    logger.warning(f"No app password found for account: {account_email}")
    logger.warning(f"Expected environment variable: {env_var_name}")
    return None

def get_account_config(account_data: Dict[str, any]) -> Dict[str, any]:
    """
    Get complete configuration for a specific email account.

    Args:
        account_data (dict): Account data from database containing email, smtp_server, etc.

    Returns:
        dict: Complete account configuration including app password
    """
    account_email = account_data.get('email')
    if not account_email:
        raise ValueError("Account data must contain 'email' field")

    # Get app password for this account
    app_password = get_account_app_password(account_email)

    # Build complete configuration
    config = {
        'sender_email': account_email,
        'app_password': app_password,
        'smtp_server': account_data.get('smtp_server', 'smtp.zoho.eu'),
        'smtp_port': account_data.get('smtp_port', 465),
        'use_ssl': account_data.get('use_ssl', True),
        'use_tls': not account_data.get('use_ssl', True),  # TLS is opposite of SSL
        'sender_name': account_data.get('display_name', account_email.split('@')[0]),
        # Copy tracking settings from global config
        'track_opens': EMAIL_CONFIG.get('track_opens', True),
        'send_follow_ups': EMAIL_CONFIG.get('send_follow_ups', True),
    }

    return config

def update_config(args):
    """Update configuration based on command line arguments."""
    # Update email configuration
    if args.password:
        EMAIL_CONFIG['app_password'] = args.password
        logger.info("Zoho Mail password provided via command line")
    elif EMAIL_CONFIG['app_password']:
        logger.info("Using Zoho Mail password from .env file")
    else:
        logger.warning("No Zoho Mail password provided. Email sending will not work.")

    if args.email:
        EMAIL_CONFIG['sender_email'] = args.email
        logger.info(f"Using Zoho Mail address: {args.email}")
    elif EMAIL_CONFIG['sender_email']:
        logger.info(f"Using Zoho Mail address from .env file: {EMAIL_CONFIG['sender_email']}")
    else:
        logger.warning("No Zoho Mail address provided. Using default: <EMAIL>")

    if args.sender_name:
        EMAIL_CONFIG['sender_name'] = args.sender_name
        logger.info(f"Using sender name: {args.sender_name}")
    elif EMAIL_CONFIG['sender_name']:
        logger.info(f"Using sender name from .env file: {EMAIL_CONFIG['sender_name']}")
    else:
        logger.warning("No sender name provided. Using default: Mateusz")

    if args.smtp_server:
        EMAIL_CONFIG['smtp_server'] = args.smtp_server
        logger.info(f"Using custom SMTP server: {args.smtp_server}")

    if args.smtp_port:
        EMAIL_CONFIG['smtp_port'] = args.smtp_port
        logger.info(f"Using custom SMTP port: {args.smtp_port}")
    elif args.tls and not args.smtp_port:
        # If TLS is requested but no port specified, use the standard TLS port
        EMAIL_CONFIG['smtp_port'] = 587
        logger.info(f"Using TLS port: 587")

    if args.tls:
        EMAIL_CONFIG['use_tls'] = True
        logger.info("Using TLS instead of SSL")

    # Update server configuration
    if args.host:
        SERVER_CONFIG['host'] = args.host
        logger.info(f"Using host: {args.host}")

    if args.port:
        SERVER_CONFIG['port'] = args.port
        logger.info(f"Using port: {args.port}")

    if args.base_url:
        SERVER_CONFIG['base_url'] = args.base_url
        logger.info(f"Using public URL for tracking: {SERVER_CONFIG['base_url']}")

    if args.no_tracking:
        EMAIL_CONFIG['track_opens'] = False
        logger.info("Email open tracking disabled")

    if args.no_follow_ups:
        EMAIL_CONFIG['send_follow_ups'] = False
        logger.info("Follow-up emails disabled")

    # Update test mode configuration
    if args.test_mode:
        SERVER_CONFIG['test_mode'] = True
        logger.info("Test mode enabled - follow-ups will be scheduled at 1-minute intervals")
        logger.info(f"Test emails will be sent to {SERVER_CONFIG['test_recipient_email']}")
