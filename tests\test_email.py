#!/usr/bin/env python3
"""
Test script for the Cold Email Server
"""
import sys
import requests
import json
import argparse

def send_test_email(server_url, recipient, subject, body, verbose=False):
    """
    Send a test email.
    
    Args:
        server_url (str): URL of the email server
        recipient (str): Email address of the recipient
        subject (str): Subject of the email
        body (str): HTML body of the email
        verbose (bool): Whether to print verbose output
        
    Returns:
        bool: True if successful, False otherwise
    """
    # Email data
    email_data = {
        "recipient": recipient,
        "subject": subject,
        "body": body
    }
    
    if verbose:
        print(f"Sending test email to {recipient}...")
        print(f"Subject: {subject}")
        print(f"Server URL: {server_url}")
    
    # Send the email
    try:
        response = requests.post(
            f"{server_url}/send-email",
            json=email_data
        )
        
        # Check response
        if response.status_code == 200:
            result = response.json()
            print("✓ Email sent successfully!")
            print(f"Tracking ID: {result.get('tracking_id')}")
            print(f"Tracking enabled: {result.get('tracking_enabled')}")
            return True
        else:
            print(f"✗ Failed to send email: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        return False

def main():
    """Main entry point."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test the Cold Email Server')
    parser.add_argument('--server', default='http://localhost:8000', help='URL of the email server')
    parser.add_argument('--recipient', default='<EMAIL>', help='Email address of the recipient')
    parser.add_argument('--subject', default='Test Email from Cold Email Server', help='Subject of the email')
    parser.add_argument('--verbose', '-v', action='store_true', help='Print verbose output')
    
    args = parser.parse_args()
    
    # HTML body for the test email
    body = """
    <html>
    <body>
        <h1>Test Email</h1>
        <p>This is a test email sent from the Cold Email Server.</p>
        <p>If you can see this, the server is working correctly!</p>
    </body>
    </html>
    """
    
    # Send the test email
    success = send_test_email(
        args.server,
        args.recipient,
        args.subject,
        body,
        args.verbose
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
