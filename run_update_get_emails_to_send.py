#!/usr/bin/env python3
"""
Run the update_get_emails_to_send.sql script to fix the get_emails_to_send function
"""
import os
import sys
import argparse
from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('update_script')

def run_sql_file(file_path):
    """
    Run a SQL file against the Supabase database.
    
    Args:
        file_path (str): Path to the SQL file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Read the SQL file
        with open(file_path, 'r') as f:
            sql = f.read()
            
        # Execute the SQL
        logger.info(f"Executing SQL from {file_path}...")
        response = db.client.rpc('exec_sql', {'sql': sql}).execute()
        
        # Check for errors
        if response.error:
            logger.error(f"Error executing SQL: {response.error}")
            return False
            
        logger.info(f"SQL executed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error running SQL file: {str(e)}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Run the update_get_emails_to_send.sql script')
    parser.add_argument('--file', default='update_get_emails_to_send.sql', help='Path to the SQL file')
    
    args = parser.parse_args()
    
    # Check if the file exists
    if not os.path.exists(args.file):
        logger.error(f"File not found: {args.file}")
        return 1
        
    # Run the SQL file
    success = run_sql_file(args.file)
    
    if success:
        logger.info("Update completed successfully")
        return 0
    else:
        logger.error("Update failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
