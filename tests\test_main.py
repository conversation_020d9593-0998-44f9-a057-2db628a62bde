#!/usr/bin/env python3
"""
Tests for the main module
"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import argparse

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from main import parse_arguments, main

class TestMain(BaseTestCase):
    """Test cases for the main module."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Mock update_config
        self.update_config_patcher = patch('main.update_config')
        self.mock_update_config = self.update_config_patcher.start()
        
        # Mock test_zoho_auth
        self.test_auth_patcher = patch('main.test_zoho_auth')
        self.mock_test_auth = self.test_auth_patcher.start()
        self.mock_test_auth.return_value = (True, 'Authentication successful')
        
        # Mock run_server
        self.run_server_patcher = patch('main.run_server')
        self.mock_run_server = self.run_server_patcher.start()
        
        # Mock argparse.ArgumentParser
        self.parser_patcher = patch('argparse.ArgumentParser')
        self.mock_parser = self.parser_patcher.start()
        self.mock_parser_instance = MagicMock()
        self.mock_parser.return_value = self.mock_parser_instance
        self.mock_args = MagicMock()
        self.mock_parser_instance.parse_args.return_value = self.mock_args
        
        # Set default values for mock args
        self.mock_args.password = None
        self.mock_args.email = None
        self.mock_args.smtp_server = None
        self.mock_args.smtp_port = None
        self.mock_args.tls = False
        self.mock_args.host = None
        self.mock_args.port = None
        self.mock_args.base_url = None
        self.mock_args.no_tracking = False
        self.mock_args.test_auth = False
        self.mock_args.no_keep_alive = False
        self.mock_args.no_follow_ups = False
        self.mock_args.no_email_processor = False
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        self.update_config_patcher.stop()
        self.test_auth_patcher.stop()
        self.run_server_patcher.stop()
        self.parser_patcher.stop()
    
    def test_parse_arguments(self):
        """Test parsing command line arguments."""
        # Mock sys.argv
        with patch('sys.argv', ['main.py', 'test_password']):
            # Call the function
            args = parse_arguments()
            
            # Check that ArgumentParser was called
            self.mock_parser.assert_called_once()
            self.mock_parser_instance.parse_args.assert_called_once()
            
            # Check that the args were returned
            self.assertEqual(args, self.mock_args)
    
    def test_main_normal(self):
        """Test the main function with normal operation."""
        # Call the function
        result = main()
        
        # Check that update_config was called
        self.mock_update_config.assert_called_once_with(self.mock_args)
        
        # Check that test_zoho_auth was not called
        self.mock_test_auth.assert_not_called()
        
        # Check that run_server was called
        self.mock_run_server.assert_called_once_with(
            host=self.mock_args.host,
            port=self.mock_args.port,
            keep_alive=True,
            process_follow_ups_enabled=True,
            process_emails_enabled=True
        )
        
        # Check the result
        self.assertEqual(result, 0)
    
    def test_main_test_auth_success(self):
        """Test the main function with test_auth=True and successful authentication."""
        # Set test_auth to True
        self.mock_args.test_auth = True
        
        # Call the function
        result = main()
        
        # Check that update_config was called
        self.mock_update_config.assert_called_once_with(self.mock_args)
        
        # Check that test_zoho_auth was called
        self.mock_test_auth.assert_called_once()
        
        # Check that run_server was not called
        self.mock_run_server.assert_not_called()
        
        # Check the result
        self.assertEqual(result, 0)
    
    def test_main_test_auth_failure(self):
        """Test the main function with test_auth=True and failed authentication."""
        # Set test_auth to True
        self.mock_args.test_auth = True
        
        # Make test_zoho_auth return failure
        self.mock_test_auth.return_value = (False, 'Authentication failed')
        
        # Call the function
        result = main()
        
        # Check that update_config was called
        self.mock_update_config.assert_called_once_with(self.mock_args)
        
        # Check that test_zoho_auth was called
        self.mock_test_auth.assert_called_once()
        
        # Check that run_server was not called
        self.mock_run_server.assert_not_called()
        
        # Check the result
        self.assertEqual(result, 1)
    
    def test_main_with_disabled_features(self):
        """Test the main function with disabled features."""
        # Set feature flags
        self.mock_args.no_keep_alive = True
        self.mock_args.no_follow_ups = True
        self.mock_args.no_email_processor = True
        
        # Call the function
        result = main()
        
        # Check that update_config was called
        self.mock_update_config.assert_called_once_with(self.mock_args)
        
        # Check that run_server was called with the correct parameters
        self.mock_run_server.assert_called_once_with(
            host=self.mock_args.host,
            port=self.mock_args.port,
            keep_alive=False,
            process_follow_ups_enabled=False,
            process_emails_enabled=False
        )
        
        # Check the result
        self.assertEqual(result, 0)

if __name__ == '__main__':
    unittest.main()
