#!/usr/bin/env python3
"""
In-memory database for tracking email opens
This will be replaced with Supabase in the future
"""
import datetime
import threading
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('tracking.database')

# Tracking database (in-memory for simplicity)
# In a production environment, you would use a real database
tracking_db = {
    'emails': {},  # Stores email metadata
    'opens': {}    # Stores email open events
}

# Lock for thread-safe database access
db_lock = threading.Lock()

def record_email_sent(tracking_id, recipient, subject):
    """
    Record that an email was sent in the tracking database.
    
    Args:
        tracking_id (str): Unique identifier for the email
        recipient (str): Email address of the recipient
        subject (str): Subject of the email
        
    Returns:
        bool: True if successful
    """
    with db_lock:
        tracking_db['emails'][tracking_id] = {
            'recipient': recipient,
            'subject': subject,
            'sent_time': datetime.datetime.now().isoformat(),
            'opens': []
        }
    logger.info(f"Recorded email with tracking ID: {tracking_id}")
    return True

def record_email_open(tracking_id, user_agent=None, ip_address=None):
    """
    Record that an email was opened in the tracking database.
    
    Args:
        tracking_id (str): Unique identifier for the email
        user_agent (str, optional): User agent of the client
        ip_address (str, optional): IP address of the client
        
    Returns:
        bool: True if successful, False if tracking ID not found
    """
    now = datetime.datetime.now().isoformat()

    with db_lock:
        # Check if the tracking ID exists
        if tracking_id not in tracking_db['emails']:
            logger.warning(f"Attempted to record open for unknown tracking ID: {tracking_id}")
            return False

        # Record the open event
        open_event = {
            'time': now,
            'user_agent': user_agent,
            'ip_address': ip_address
        }

        tracking_db['emails'][tracking_id]['opens'].append(open_event)

        # Also store in the opens collection for quick lookup
        if tracking_id not in tracking_db['opens']:
            tracking_db['opens'][tracking_id] = []

        tracking_db['opens'][tracking_id].append(open_event)

        logger.info(f"Recorded email open for tracking ID: {tracking_id}")
        return True

def get_email_data(tracking_id):
    """
    Get data for a specific email by tracking ID.
    
    Args:
        tracking_id (str): Unique identifier for the email
        
    Returns:
        dict: Email data or None if not found
    """
    with db_lock:
        if tracking_id in tracking_db['emails']:
            return tracking_db['emails'][tracking_id]
        return None

def get_tracking_summary():
    """
    Get a summary of all tracked emails.
    
    Returns:
        dict: Summary of all tracked emails
    """
    with db_lock:
        summary = {
            "total_emails": len(tracking_db['emails']),
            "total_opens": sum(len(opens) for opens in tracking_db['opens'].values()),
            "emails": {}
        }

        for tracking_id, email_data in tracking_db['emails'].items():
            summary['emails'][tracking_id] = {
                "recipient": email_data['recipient'],
                "subject": email_data['subject'],
                "sent_time": email_data['sent_time'],
                "opens": len(email_data['opens'])
            }

        return summary
