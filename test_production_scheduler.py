#!/usr/bin/env python3
"""
Test script for the production email scheduling system.
Demonstrates quota management, business hours enforcement, and daily preprocessing.
"""
import os
import sys
import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv(override=True)

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.scheduling.daily_preprocessor import DailyPreprocessor
from app.scheduling.quota_manager import QuotaManager, EmailItem
from app.scheduling.scheduler_service import scheduler_service
from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('test_scheduler')

def test_quota_manager():
    """Test the quota management system."""
    print("=== Testing Quota Manager ===")
    
    quota_manager = QuotaManager()
    
    # Initialize quota for a test account
    account_id = "test-account-123"
    daily_limit = 10
    quota = quota_manager.initialize_account_quota(account_id, daily_limit)
    
    print(f"Account: {account_id}")
    print(f"Daily Limit: {quota.daily_limit}")
    print(f"Follow-up Quota: {quota.followup_quota} (70%)")
    print(f"Initial Quota: {quota.initial_quota} (30%)")
    
    # Test quota allocation
    print("\nTesting quota allocation:")
    
    # Allocate follow-up emails
    for i in range(8):  # Try to allocate 8 follow-ups (quota is 7)
        success = quota.allocate_followup()
        print(f"Follow-up {i+1}: {'✓' if success else '✗'} (Used: {quota.followup_used}/{quota.followup_quota})")
    
    # Allocate initial emails
    for i in range(5):  # Try to allocate 5 initial emails (quota is 3, but can use leftover)
        success = quota.allocate_initial()
        print(f"Initial {i+1}: {'✓' if success else '✗'} (Used: {quota.initial_used}/{quota.initial_quota}, Total: {quota.followup_used + quota.initial_used}/{quota.daily_limit})")
    
    print()

def test_business_hours():
    """Test business hours enforcement."""
    print("=== Testing Business Hours Enforcement ===")
    
    quota_manager = QuotaManager()
    
    # Test various times
    test_times = [
        datetime.datetime(2024, 1, 15, 8, 30),   # 8:30 AM - before business hours
        datetime.datetime(2024, 1, 15, 9, 0),    # 9:00 AM - start of business hours
        datetime.datetime(2024, 1, 15, 12, 30),  # 12:30 PM - during business hours
        datetime.datetime(2024, 1, 15, 17, 0),   # 5:00 PM - end of business hours
        datetime.datetime(2024, 1, 15, 18, 30),  # 6:30 PM - after business hours
    ]
    
    for test_time in test_times:
        is_business = quota_manager.is_within_business_hours(test_time)
        adjusted_time = quota_manager.adjust_to_business_hours(test_time)
        
        print(f"Time: {test_time.strftime('%H:%M')} - Business Hours: {'✓' if is_business else '✗'}")
        if test_time != adjusted_time:
            print(f"  Adjusted to: {adjusted_time.strftime('%Y-%m-%d %H:%M')}")
    
    print()

def test_email_postponement():
    """Test email postponement logic."""
    print("=== Testing Email Postponement ===")
    
    quota_manager = QuotaManager()
    
    # Create a test email
    original_time = datetime.datetime(2024, 1, 15, 14, 30)  # 2:30 PM
    email = EmailItem(
        email_id="test-email-123",
        account_id="test-account-123",
        scheduled_time=original_time,
        is_followup=True
    )
    
    print(f"Original scheduled time: {email.scheduled_time}")
    
    # Postpone the email
    postponed_email = quota_manager.postpone_email(email)
    
    print(f"Postponed scheduled time: {postponed_email.scheduled_time}")
    print(f"Postponed flag: {postponed_email.postponed}")
    print(f"Days postponed: {(postponed_email.scheduled_time.date() - original_time.date()).days}")
    
    print()

def create_test_emails():
    """Create some test emails in the database for demonstration."""
    print("=== Creating Test Emails ===")
    
    try:
        # Get first account and recipient
        accounts = db.get_email_accounts(active_only=True)
        recipients = db.get_recipients()
        
        if not accounts or not recipients:
            print("❌ No accounts or recipients found in database")
            return
        
        account = accounts[0]
        recipient = recipients[0]
        
        print(f"Using account: {account['email']}")
        print(f"Using recipient: {recipient['email']}")
        
        # Create test emails for today
        today = datetime.date.today()
        base_time = datetime.datetime.combine(today, datetime.time(10, 0))  # 10:00 AM
        
        test_emails = [
            {
                'subject': 'Test Initial Email 1',
                'body': '<p>This is a test initial email.</p>',
                'scheduled_time': base_time,
                'is_followup': False
            },
            {
                'subject': 'Test Follow-up Email 1',
                'body': '<p>This is a test follow-up email.</p>',
                'scheduled_time': base_time + datetime.timedelta(hours=1),
                'is_followup': True
            },
            {
                'subject': 'Test Initial Email 2',
                'body': '<p>This is another test initial email.</p>',
                'scheduled_time': base_time + datetime.timedelta(hours=2),
                'is_followup': False
            },
            {
                'subject': 'Test Follow-up Email 2',
                'body': '<p>This is another test follow-up email.</p>',
                'scheduled_time': base_time + datetime.timedelta(hours=3),
                'is_followup': True
            }
        ]
        
        created_count = 0
        for email_data in test_emails:
            try:
                # Create email content
                content_response = db.client.table('email_content').insert({
                    'subject': email_data['subject'],
                    'body': email_data['body']
                }).execute()
                content_result = db._handle_response(content_response)
                
                if content_result and len(content_result) > 0:
                    content_id = content_result[0]['id']
                    
                    # Create email queue entry
                    queue_data = {
                        'account_id': account['id'],
                        'recipient_id': recipient['id'],
                        'content_id': content_id,
                        'scheduled_time': email_data['scheduled_time'].isoformat(),
                        'status': 'scheduled',
                        'is_follow_up': email_data['is_followup'],
                        'follow_up': 1 if email_data['is_followup'] else 0
                    }
                    
                    queue_response = db.client.table('email_queue').insert(queue_data).execute()
                    queue_result = db._handle_response(queue_response)
                    
                    if queue_result and len(queue_result) > 0:
                        created_count += 1
                        print(f"✓ Created: {email_data['subject']} at {email_data['scheduled_time']}")
                    
            except Exception as e:
                print(f"❌ Error creating email: {str(e)}")
        
        print(f"\nCreated {created_count} test emails")
        
    except Exception as e:
        print(f"❌ Error creating test emails: {str(e)}")
    
    print()

def test_daily_preprocessing():
    """Test the daily preprocessing system."""
    print("=== Testing Daily Preprocessing ===")
    
    try:
        preprocessor = DailyPreprocessor()
        
        # Run preprocessing for today
        results = preprocessor.run_daily_preprocessing()
        
        print("Preprocessing Results:")
        print(f"Date: {results['date']}")
        print(f"Accounts processed: {results['accounts_processed']}")
        print(f"Emails scheduled: {results['emails_scheduled']}")
        print(f"Emails postponed: {results['emails_postponed']}")
        
        print("\nDaily queues:")
        for account_id, queue_size in results['daily_queues'].items():
            print(f"  Account {account_id}: {queue_size} emails")
        
        print("\nStatistics:")
        stats = results['statistics']
        print(f"Total emails processed: {stats['total_emails_processed']}")
        print(f"Follow-ups: {stats['emails_by_type']['followups']}")
        print(f"Initial emails: {stats['emails_by_type']['initials']}")
        
        print("\nQuota utilization:")
        for account_id, utilization in stats['quota_utilization'].items():
            print(f"  Account {account_id}:")
            print(f"    Daily limit: {utilization['daily_limit']}")
            print(f"    Follow-up utilization: {utilization['followup_utilization_pct']:.1f}%")
            print(f"    Initial utilization: {utilization['initial_utilization_pct']:.1f}%")
            print(f"    Total utilization: {utilization['total_utilization_pct']:.1f}%")
        
    except Exception as e:
        print(f"❌ Error in daily preprocessing: {str(e)}")
    
    print()

def test_scheduler_service():
    """Test the scheduler service."""
    print("=== Testing Scheduler Service ===")
    
    try:
        # Get scheduler status
        status = scheduler_service.get_status()
        
        print("Scheduler Status:")
        print(f"Running: {status['is_running']}")
        print(f"Current time: {status['current_time']}")
        print(f"Business hours: {status['is_business_hours']}")
        print(f"Preprocessor ready: {status['preprocessor_ready']}")
        
        if status['queue_status']:
            print("\nQueue status:")
            for account_id, queue_info in status['queue_status'].items():
                print(f"  Account {account_id}: {queue_info['emails_remaining']} emails remaining")
                if queue_info['next_email_time']:
                    print(f"    Next email: {queue_info['next_email_time']}")
        
        # Force preprocessing for testing
        print("\nForcing preprocessing...")
        preprocessing_results = scheduler_service.force_preprocessing()
        
        if 'results' in preprocessing_results:
            results = preprocessing_results['results']
            print(f"✓ Preprocessing completed: {results['emails_scheduled']} scheduled, {results['emails_postponed']} postponed")
        
    except Exception as e:
        print(f"❌ Error testing scheduler service: {str(e)}")
    
    print()

def main():
    """Main test function."""
    print("Production Email Scheduler Test")
    print("=" * 50)
    
    # Run individual tests
    test_quota_manager()
    test_business_hours()
    test_email_postponement()
    
    # Test with real data
    create_test_emails()
    test_daily_preprocessing()
    test_scheduler_service()
    
    print("✅ All tests completed!")
    print("\nNext steps:")
    print("1. Start the scheduler service: scheduler_service.start()")
    print("2. Monitor via /scheduler-status endpoint")
    print("3. Force preprocessing via /force-preprocessing endpoint")

if __name__ == "__main__":
    main()
