#!/usr/bin/env python3
"""
Tests for the email sender module
"""
import unittest
from unittest.mock import patch, MagicMock, call
import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.email.sender import send_email, test_zoho_auth
from app.config import EMAIL_CONFIG

class TestEmailSender(BaseTestCase):
    """Test cases for the email sender module."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Set up tracking pixel mock
        self.tracking_id = '00000000-0000-0000-0000-000000000009'
        self.tracking_pixel_patcher = patch('app.tracking.pixels.generate_tracking_id')
        self.mock_generate_tracking_id = self.tracking_pixel_patcher.start()
        self.mock_generate_tracking_id.return_value = self.tracking_id
        
        self.add_tracking_pixel_patcher = patch('app.tracking.pixels.add_tracking_pixel')
        self.mock_add_tracking_pixel = self.add_tracking_pixel_patcher.start()
        self.mock_add_tracking_pixel.side_effect = lambda body, tracking_id: f"{body}<img src='tracking_pixel_{tracking_id}'/>"
        
        # Set up record_email_sent mock
        self.record_email_patcher = patch('app.tracking.database.record_email_sent')
        self.mock_record_email = self.record_email_patcher.start()
        self.mock_record_email.return_value = True
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        self.tracking_pixel_patcher.stop()
        self.add_tracking_pixel_patcher.stop()
        self.record_email_patcher.stop()
    
    def test_send_email_success(self):
        """Test sending an email successfully."""
        # Set up test data
        recipient = '<EMAIL>'
        subject = 'Test Subject'
        body = '<html><body><p>Test body</p></body></html>'
        
        # Call the function
        success, result = send_email(recipient, subject, body)
        
        # Check that the email was sent successfully
        self.assertTrue(success)
        self.assertEqual(result, self.tracking_id)
        
        # Check that the SMTP server was used correctly
        self.mock_smtp.assert_called_once()
        self.mock_server.login.assert_called_once_with(
            EMAIL_CONFIG['sender_email'],
            EMAIL_CONFIG['app_password']
        )
        self.mock_server.send_message.assert_called_once()
        self.mock_server.quit.assert_called_once()
        
        # Check that tracking was set up correctly
        self.mock_generate_tracking_id.assert_called_once()
        self.mock_add_tracking_pixel.assert_called_once_with(body, self.tracking_id)
        self.mock_record_email.assert_called_once_with(self.tracking_id, recipient, subject)
    
    def test_send_email_no_password(self):
        """Test sending an email with no password (simulation mode)."""
        # Save original password
        original_password = EMAIL_CONFIG['app_password']
        
        # Set password to empty string
        EMAIL_CONFIG['app_password'] = ''
        
        # Set up test data
        recipient = '<EMAIL>'
        subject = 'Test Subject'
        body = '<html><body><p>Test body</p></body></html>'
        
        # Call the function
        success, result = send_email(recipient, subject, body)
        
        # Check that the email was "sent" successfully
        self.assertTrue(success)
        self.assertEqual(result, self.tracking_id)
        
        # Check that the SMTP server was NOT used
        self.mock_smtp.assert_not_called()
        self.mock_server.login.assert_not_called()
        self.mock_server.send_message.assert_not_called()
        
        # Check that tracking was set up correctly
        self.mock_generate_tracking_id.assert_called_once()
        self.mock_add_tracking_pixel.assert_called_once_with(body, self.tracking_id)
        self.mock_record_email.assert_called_once_with(self.tracking_id, recipient, subject)
        
        # Restore original password
        EMAIL_CONFIG['app_password'] = original_password
    
    def test_send_email_smtp_error(self):
        """Test sending an email with SMTP error."""
        # Set up test data
        recipient = '<EMAIL>'
        subject = 'Test Subject'
        body = '<html><body><p>Test body</p></body></html>'
        
        # Make the SMTP server raise an exception
        self.mock_server.login.side_effect = smtplib.SMTPAuthenticationError(535, b'Authentication failed')
        
        # Call the function
        success, result = send_email(recipient, subject, body)
        
        # Check that the email was not sent
        self.assertFalse(success)
        self.assertIn('Error', result)
        self.assertIn('Authentication failed', result)
        
        # Check that the SMTP server was used correctly
        self.mock_smtp.assert_called_once()
        self.mock_server.login.assert_called_once_with(
            EMAIL_CONFIG['sender_email'],
            EMAIL_CONFIG['app_password']
        )
        self.mock_server.send_message.assert_not_called()
        self.mock_server.quit.assert_called_once()
        
        # Check that tracking was set up correctly
        self.mock_generate_tracking_id.assert_called_once()
        self.mock_add_tracking_pixel.assert_called_once_with(body, self.tracking_id)
        self.mock_record_email.assert_not_called()
    
    def test_test_zoho_auth_success(self):
        """Test Zoho authentication success."""
        # Call the function
        success, message = test_zoho_auth()
        
        # Check the result
        self.assertTrue(success)
        self.assertEqual(message, 'Authentication successful')
        
        # Check that the SMTP server was used correctly
        self.mock_smtp.assert_called_once()
        self.mock_server.login.assert_called_once_with(
            EMAIL_CONFIG['sender_email'],
            EMAIL_CONFIG['app_password']
        )
        self.mock_server.quit.assert_called_once()
    
    def test_test_zoho_auth_failure(self):
        """Test Zoho authentication failure."""
        # Make the SMTP server raise an exception
        self.mock_server.login.side_effect = smtplib.SMTPAuthenticationError(535, b'Authentication failed')
        
        # Call the function
        success, message = test_zoho_auth()
        
        # Check the result
        self.assertFalse(success)
        self.assertIn('Error', message)
        self.assertIn('Authentication failed', message)
        
        # Check that the SMTP server was used correctly
        self.mock_smtp.assert_called_once()
        self.mock_server.login.assert_called_once_with(
            EMAIL_CONFIG['sender_email'],
            EMAIL_CONFIG['app_password']
        )
        self.mock_server.quit.assert_called_once()

if __name__ == '__main__':
    unittest.main()
