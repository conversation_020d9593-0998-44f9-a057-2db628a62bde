#!/usr/bin/env python3
"""
Test database functions after SQL updates
"""
import sys
from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('test_script')

def test_get_emails_to_send():
    """
    Test the get_emails_to_send function
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("Testing get_emails_to_send function...")
        response = db.client.rpc(
            'get_emails_to_send',
            {'time_window_minutes': 60}
        ).execute()
        
        if response.error:
            logger.error(f"Error calling get_emails_to_send: {response.error}")
            return False
        
        result = db._handle_response(response)
        logger.info(f"get_emails_to_send returned {len(result) if result else 0} emails")
        
        # Log the first few emails for debugging
        if result:
            for i, email in enumerate(result[:3]):  # Log up to 3 emails
                scheduled_time = email.get('scheduled_time')
                subject = email.get('subject')
                recipient = email.get('recipient_email')
                content_id = email.get('content_id')
                logger.info(f"  Email {i+1}: ID={email.get('id')}, To={recipient}, Subject={subject}, Content ID={content_id}, Scheduled={scheduled_time}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing get_emails_to_send: {str(e)}")
        return False

def test_get_email_by_id():
    """
    Test the get_email_by_id method
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("Testing get_email_by_id method...")
        
        # First, get an email ID from the queue
        response = db.client.table('email_queue').select('id').limit(1).execute()
        result = db._handle_response(response)
        
        if not result or len(result) == 0:
            logger.warning("No emails found in the queue to test get_email_by_id")
            return True
        
        email_id = result[0]['id']
        logger.info(f"Testing get_email_by_id with email ID: {email_id}")
        
        # Get the email by ID
        email = db.get_email_by_id(email_id)
        
        if not email:
            logger.error(f"get_email_by_id returned None for email ID: {email_id}")
            return False
        
        # Check if the email has subject and body
        if 'subject' not in email:
            logger.error(f"Email does not have a subject field: {email}")
            return False
        
        if 'body' not in email:
            logger.error(f"Email does not have a body field: {email}")
            return False
        
        logger.info(f"get_email_by_id returned email with subject: {email['subject']}")
        return True
    except Exception as e:
        logger.error(f"Error testing get_email_by_id: {str(e)}")
        return False

def main():
    """Main entry point."""
    logger.info("Starting database function tests...")
    
    # Test the get_emails_to_send function
    get_emails_success = test_get_emails_to_send()
    
    # Test the get_email_by_id method
    get_email_success = test_get_email_by_id()
    
    if get_emails_success and get_email_success:
        logger.info("All tests passed!")
        return 0
    else:
        logger.error("One or more tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
