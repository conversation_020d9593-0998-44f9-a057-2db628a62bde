#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean up the email queue by removing records with status: failed, scheduled, or sending.
Also removes corresponding records in related tables (email_tracking and link_tracking).
"""
import os
import sys
import argparse
from typing import Dict, Any, List, Tuple

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from app.database.supabase_client import db
    from app.utils.logger import setup_logger
except ImportError:
    print("Error: Could not import required modules from the app package.")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger('clean_email_queue')


def execute_cleanup() -> bool:
    """
    Clean up the email queue by removing records with status: failed, scheduled, or sending.
    Also removes corresponding records in related tables (email_tracking and link_tracking).

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # First, get a count of emails with each status before deletion
        logger.info("Counting emails by status before deletion...")

        # Get all statuses
        statuses = ['scheduled', 'sending', 'sent', 'failed', 'cancelled']
        status_counts = {}

        # Count emails for each status
        for status in statuses:
            response = db.client.table('email_queue').select('count').eq('status', status).execute()
            result = db._handle_response(response)
            if result and len(result) > 0:
                count = len(result)
                status_counts[status] = count

        # Count total emails
        response = db.client.table('email_queue').select('count').execute()
        result = db._handle_response(response)
        total_count = len(result) if result else 0

        logger.info(f"Total emails in queue: {total_count}")
        logger.info("Current email counts by status:")
        for status, count in status_counts.items():
            logger.info(f"  {status}: {count}")

        # Count emails that will be deleted
        response = db.client.table('email_queue').select('id').in_('status', ['failed', 'scheduled', 'sending']).execute()
        result = db._handle_response(response)
        to_delete_count = len(result) if result else 0
        logger.info(f"Will delete {to_delete_count} emails with status: failed, scheduled, or sending")

        # Execute the SQL script
        logger.info("Executing SQL script to clean up email queue...")

        # Since we can't directly execute arbitrary SQL, we'll delete records manually

        # First, get all emails to delete
        response = db.client.table('email_queue').select('id, tracking_id').in_('status', ['failed', 'scheduled', 'sending']).execute()
        emails_to_delete = db._handle_response(response)

        if not emails_to_delete:
            logger.info("No emails found to delete")
            return True

        email_ids = [email['id'] for email in emails_to_delete if 'id' in email]
        tracking_ids = [email['tracking_id'] for email in emails_to_delete if 'tracking_id' in email]

        # Delete from link_tracking
        deleted_link_count = 0
        if email_ids:
            response = db.client.table('link_tracking').select('count').in_('email_id', email_ids).execute()
            result = db._handle_response(response)
            deleted_link_count = len(result) if result else 0

            if deleted_link_count > 0:
                db.client.table('link_tracking').delete().in_('email_id', email_ids).execute()

        if tracking_ids:
            response = db.client.table('link_tracking').select('count').in_('tracking_id', tracking_ids).execute()
            result = db._handle_response(response)
            deleted_link_count += len(result) if result else 0

            if deleted_link_count > 0:
                db.client.table('link_tracking').delete().in_('tracking_id', tracking_ids).execute()

        logger.info(f"Deleted {deleted_link_count} records from link_tracking table")

        # Delete from email_tracking
        deleted_tracking_count = 0
        if email_ids:
            response = db.client.table('email_tracking').select('count').in_('email_id', email_ids).execute()
            result = db._handle_response(response)
            deleted_tracking_count = len(result) if result else 0

            if deleted_tracking_count > 0:
                db.client.table('email_tracking').delete().in_('email_id', email_ids).execute()

        if tracking_ids:
            response = db.client.table('email_tracking').select('count').in_('tracking_id', tracking_ids).execute()
            result = db._handle_response(response)
            deleted_tracking_count += len(result) if result else 0

            if deleted_tracking_count > 0:
                db.client.table('email_tracking').delete().in_('tracking_id', tracking_ids).execute()

        logger.info(f"Deleted {deleted_tracking_count} records from email_tracking table")

        # Delete from email_queue
        if email_ids:
            db.client.table('email_queue').delete().in_('id', email_ids).execute()
            logger.info(f"Deleted {len(email_ids)} records from email_queue table")

        # Get the count of emails with each status after deletion
        logger.info("Counting emails by status after deletion...")

        # Count emails for each status after deletion
        status_counts_after = {}
        for status in statuses:
            response = db.client.table('email_queue').select('count').eq('status', status).execute()
            result = db._handle_response(response)
            if result and len(result) > 0:
                count = len(result)
                status_counts_after[status] = count

        # Count total emails after deletion
        response = db.client.table('email_queue').select('count').execute()
        result = db._handle_response(response)
        total_count_after = len(result) if result else 0

        logger.info(f"Total emails in queue after cleanup: {total_count_after}")
        logger.info("Email counts by status after cleanup:")
        for status, count in status_counts_after.items():
            logger.info(f"  {status}: {count}")

        logger.info("Email queue cleanup completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error cleaning up email queue: {str(e)}")
        return False

def main():
    """Main function to clean up the email queue."""
    parser = argparse.ArgumentParser(description='Clean up the email queue by removing records with specific statuses.')
    parser.add_argument('--dry-run', action='store_true',
                        help='Perform a dry run without actually deleting records')

    args = parser.parse_args()

    if args.dry_run:
        logger.info("DRY RUN MODE: No records will be deleted")

        # Get all statuses
        statuses = ['scheduled', 'sending', 'sent', 'failed', 'cancelled']
        status_counts = {}

        # Count emails for each status
        for status in statuses:
            response = db.client.table('email_queue').select('count').eq('status', status).execute()
            result = db._handle_response(response)
            if result and len(result) > 0:
                count = len(result)
                status_counts[status] = count

        # Count total emails
        response = db.client.table('email_queue').select('count').execute()
        result = db._handle_response(response)
        total_count = len(result) if result else 0

        logger.info(f"Total emails in queue: {total_count}")
        logger.info("Current email counts by status:")
        for status, count in status_counts.items():
            logger.info(f"  {status}: {count}")

        # Count emails that would be deleted
        response = db.client.table('email_queue').select('id').in_('status', ['failed', 'scheduled', 'sending']).execute()
        result = db._handle_response(response)
        to_delete_count = len(result) if result else 0
        logger.info(f"Would delete {to_delete_count} emails with status: failed, scheduled, or sending")

        return

    # Execute the cleanup
    success = execute_cleanup()

    if success:
        logger.info("Email queue cleanup completed successfully")
    else:
        logger.error("Email queue cleanup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
