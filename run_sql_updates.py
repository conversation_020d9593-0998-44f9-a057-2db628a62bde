#!/usr/bin/env python3
"""
Run SQL updates to fix the database functions for email content
"""
import os
import sys
import argparse
from app.database.supabase_client import db
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('update_script')

def run_sql_file(file_path):
    """
    Run a SQL file against the Supabase database.
    
    Args:
        file_path (str): Path to the SQL file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Read the SQL file
        with open(file_path, 'r') as f:
            sql = f.read()
            
        # Execute the SQL
        logger.info(f"Executing SQL from {file_path}...")
        
        # First, create the exec_sql function if it doesn't exist
        create_exec_sql = """
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        """
        
        db.client.rpc('exec_sql', {'sql': create_exec_sql}).execute()
        
        # Now execute the actual SQL
        response = db.client.rpc('exec_sql', {'sql': sql}).execute()
        
        # Check for errors
        if response.error:
            logger.error(f"Error executing SQL: {response.error}")
            return False
            
        logger.info(f"SQL executed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error running SQL file: {str(e)}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Run SQL updates for email content')
    parser.add_argument('--files', nargs='+', default=['update_get_emails_to_send.sql', 'update_schedule_follow_up_email.sql'], 
                        help='Paths to the SQL files')
    
    args = parser.parse_args()
    
    success = True
    
    for file_path in args.files:
        # Check if the file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            success = False
            continue
            
        # Run the SQL file
        if not run_sql_file(file_path):
            success = False
    
    if success:
        logger.info("All updates completed successfully")
        return 0
    else:
        logger.error("One or more updates failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
