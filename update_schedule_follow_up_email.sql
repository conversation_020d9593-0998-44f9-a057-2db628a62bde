-- Update the schedule_follow_up_email function to use the email_content table
CREATE OR REPLACE FUNCTION schedule_follow_up_email(
  email_id UUID,
  follow_up_days INTEGER,
  follow_up_subject VARCHAR(255) DEFAULT NULL,
  follow_up_body TEXT DEFAULT NULL
)
R<PERSON><PERSON>NS UUID AS $$
DECLARE
  previous_email RECORD;
  follow_up_id UUID;
  follow_up_num INTEGER;
  follow_up_scheduled_time TIMESTAMP WITH TIME ZONE;
  template_id UUID;
BEGIN
  -- Get the previous email details
  SELECT
    eq.*,
    r.email AS recipient_email
  INTO previous_email
  FROM email_queue eq
  JOIN recipients r ON eq.recipient_id = r.id
  WHERE eq.id = email_id;

  IF previous_email IS NULL THEN
    RAISE EXCEPTION 'Email with ID % not found', email_id;
  END IF;

  -- Calculate the follow-up number
  follow_up_num := COALESCE(previous_email.follow_up, 0) + 1;

  -- Determine the template ID based on follow-up number
  CASE
    WHEN follow_up_num = 1 THEN template_id := '00000000-0000-0000-0000-000000000002'::UUID;
    WHEN follow_up_num = 2 THEN template_id := '00000000-0000-0000-0000-000000000003'::UUID;
    WHEN follow_up_num = 3 THEN template_id := '00000000-0000-0000-0000-000000000004'::UUID;
    WHEN follow_up_num = 4 THEN template_id := '00000000-0000-0000-0000-000000000005'::UUID;
    ELSE template_id := '00000000-0000-0000-0000-000000000002'::UUID;
  END CASE;

  -- Calculate the scheduled time for the follow-up
  -- Add the specified number of days to the current time
  follow_up_scheduled_time := NOW() + (follow_up_days || ' days')::INTERVAL;

  -- Adjust for weekends if needed
  -- If the scheduled time falls on a Saturday, move to Monday
  IF EXTRACT(DOW FROM follow_up_scheduled_time) = 6 THEN
    follow_up_scheduled_time := follow_up_scheduled_time + '2 days'::INTERVAL;
  -- If the scheduled time falls on a Sunday, move to Monday
  ELSIF EXTRACT(DOW FROM follow_up_scheduled_time) = 0 THEN
    follow_up_scheduled_time := follow_up_scheduled_time + '1 day'::INTERVAL;
  END IF;

  -- Insert the follow-up email into the queue
  INSERT INTO email_queue (
    account_id,
    recipient_id,
    content_id,
    scheduled_time,
    is_follow_up,
    follow_up,
    previous_email_id
  ) VALUES (
    previous_email.account_id,
    previous_email.recipient_id,
    template_id,
    follow_up_scheduled_time,
    TRUE,
    follow_up_num,
    email_id
  ) RETURNING id INTO follow_up_id;

  RETURN follow_up_id;
END;
$$ LANGUAGE plpgsql;
