#!/usr/bin/env python3
"""
Test script to work with the mock database
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(override=True)

# Add the current directory to the path so we can import the app modules
sys.path.append('.')

from app.database.supabase_client import SupabaseClient
from app.email.sender import send_email
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger()

def main():
    """Main function to test database operations with mock data."""
    # Create a mock database client
    # Force using mock database by setting client to None
    db = SupabaseClient()
    db.client = None
    db._mock_db = {
        'email_accounts': [],
        'recipients': [],
        'email_queue': [],
        'email_tracking': [],
        'link_tracking': [],
        'server_status': []
    }

    print("Using mock database")

    # Create a test recipient
    print("\nCreating a test recipient...")
    recipient = db.create_recipient(
        email="<EMAIL>",
        first_name="MatHat",
        last_name="Contact",
        company="MatHat"
    )
    print(f"Created recipient: {recipient.get('email')}, ID: {recipient.get('id')}")

    # Create a test email account
    print("\nCreating a test email account...")
    account = db.create_email_account(
        email="<EMAIL>",
        display_name="Mateusz",
        smtp_server="smtp.zoho.eu",
        smtp_port=465,
        use_ssl=True,
        daily_limit=10
    )
    print(f"Created account: {account.get('email')}, ID: {account.get('id')}")

    # Get all recipients
    recipients = db.get_recipients()
    print(f"\nFound {len(recipients)} recipients:")
    for r in recipients:
        print(f"- {r.get('email')}, ID: {r.get('id')}")

    # Get all email accounts
    accounts = db.get_email_accounts()
    print(f"\nFound {len(accounts)} email accounts:")
    for a in accounts:
        print(f"- {a.get('email')}, ID: {a.get('id')}")

    # Send a test email
    print("\nSending a test email...")

    # Define email content
    subject = "Test Email from ColdMails"
    body = """
    <html>
    <body>
        <h1>Test Email</h1>
        <p>This is a test email sent from the ColdMails application.</p>
        <p>If you're seeing this, the email sending functionality is working correctly.</p>
        <p>Best regards,<br>ColdMails Test</p>
    </body>
    </html>
    """

    # Get the app password from the environment
    app_password = os.getenv('ZOHO_APP_PASSWORD')
    if not app_password or app_password == 'your-actual-app-password-here':
        print("No valid app password found in .env file. Cannot send email.")
        print("Please update the ZOHO_APP_PASSWORD in your .env file with a valid app password.")
        return

    # Send the email
    success, result = send_email(recipient.get('email'), subject, body)

    if success:
        print(f"Email sent successfully! Tracking ID: {result}")
    else:
        print(f"Failed to send email: {result}")

if __name__ == "__main__":
    main()
