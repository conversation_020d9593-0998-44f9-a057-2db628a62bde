#!/usr/bin/env python3
"""
Tests for the tracking database module
"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import datetime
import threading

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.tracking.database import (
    record_email_sent,
    record_email_open,
    get_email_data,
    get_tracking_summary,
    tracking_db,
    db_lock
)

class TestTrackingDatabase(BaseTestCase):
    """Test cases for the tracking database module."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Clear the tracking database before each test
        with db_lock:
            tracking_db['emails'] = {}
            tracking_db['opens'] = {}
        
        # Mock datetime.datetime.now
        self.datetime_patcher = patch('datetime.datetime')
        self.mock_datetime = self.datetime_patcher.start()
        self.mock_now = MagicMock()
        self.mock_now.isoformat.return_value = '2021-01-01T00:00:00'
        self.mock_datetime.now.return_value = self.mock_now
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        self.datetime_patcher.stop()
    
    def test_record_email_sent(self):
        """Test recording an email sent."""
        # Set up test data
        tracking_id = '00000000-0000-0000-0000-000000000013'
        recipient = '<EMAIL>'
        subject = 'Test Subject'
        
        # Call the function
        result = record_email_sent(tracking_id, recipient, subject)
        
        # Check the result
        self.assertTrue(result)
        
        # Check that the email was recorded in the database
        self.assertIn(tracking_id, tracking_db['emails'])
        self.assertEqual(tracking_db['emails'][tracking_id]['recipient'], recipient)
        self.assertEqual(tracking_db['emails'][tracking_id]['subject'], subject)
        self.assertEqual(tracking_db['emails'][tracking_id]['sent_time'], '2021-01-01T00:00:00')
        self.assertEqual(tracking_db['emails'][tracking_id]['opens'], [])
    
    def test_record_email_open(self):
        """Test recording an email open."""
        # Set up test data
        tracking_id = '00000000-0000-0000-0000-000000000013'
        recipient = '<EMAIL>'
        subject = 'Test Subject'
        user_agent = 'Mozilla/5.0'
        ip_address = '127.0.0.1'
        
        # Record the email first
        record_email_sent(tracking_id, recipient, subject)
        
        # Call the function
        result = record_email_open(tracking_id, user_agent, ip_address)
        
        # Check the result
        self.assertTrue(result)
        
        # Check that the open was recorded in the database
        self.assertIn(tracking_id, tracking_db['emails'])
        self.assertEqual(len(tracking_db['emails'][tracking_id]['opens']), 1)
        self.assertEqual(tracking_db['emails'][tracking_id]['opens'][0]['time'], '2021-01-01T00:00:00')
        self.assertEqual(tracking_db['emails'][tracking_id]['opens'][0]['user_agent'], user_agent)
        self.assertEqual(tracking_db['emails'][tracking_id]['opens'][0]['ip_address'], ip_address)
        
        # Check that the open was also recorded in the opens collection
        self.assertIn(tracking_id, tracking_db['opens'])
        self.assertEqual(len(tracking_db['opens'][tracking_id]), 1)
        self.assertEqual(tracking_db['opens'][tracking_id][0]['time'], '2021-01-01T00:00:00')
        self.assertEqual(tracking_db['opens'][tracking_id][0]['user_agent'], user_agent)
        self.assertEqual(tracking_db['opens'][tracking_id][0]['ip_address'], ip_address)
    
    def test_record_email_open_unknown_id(self):
        """Test recording an open for an unknown tracking ID."""
        # Set up test data
        tracking_id = '00000000-0000-0000-0000-000000000013'
        user_agent = 'Mozilla/5.0'
        ip_address = '127.0.0.1'
        
        # Call the function without recording the email first
        result = record_email_open(tracking_id, user_agent, ip_address)
        
        # Check the result
        self.assertFalse(result)
        
        # Check that nothing was recorded in the database
        self.assertNotIn(tracking_id, tracking_db['emails'])
        self.assertNotIn(tracking_id, tracking_db['opens'])
    
    def test_get_email_data(self):
        """Test getting email data."""
        # Set up test data
        tracking_id = '00000000-0000-0000-0000-000000000013'
        recipient = '<EMAIL>'
        subject = 'Test Subject'
        
        # Record the email
        record_email_sent(tracking_id, recipient, subject)
        
        # Call the function
        result = get_email_data(tracking_id)
        
        # Check the result
        self.assertIsNotNone(result)
        self.assertEqual(result['recipient'], recipient)
        self.assertEqual(result['subject'], subject)
        self.assertEqual(result['sent_time'], '2021-01-01T00:00:00')
        self.assertEqual(result['opens'], [])
        
        # Test with unknown tracking ID
        result = get_email_data('unknown-id')
        self.assertIsNone(result)
    
    def test_get_tracking_summary(self):
        """Test getting tracking summary."""
        # Set up test data
        tracking_id1 = '00000000-0000-0000-0000-000000000013'
        recipient1 = '<EMAIL>'
        subject1 = 'Test Subject 1'
        
        tracking_id2 = '00000000-0000-0000-0000-000000000014'
        recipient2 = '<EMAIL>'
        subject2 = 'Test Subject 2'
        
        # Record the emails
        record_email_sent(tracking_id1, recipient1, subject1)
        record_email_sent(tracking_id2, recipient2, subject2)
        
        # Record some opens
        record_email_open(tracking_id1, 'Mozilla/5.0', '127.0.0.1')
        record_email_open(tracking_id1, 'Chrome', '***********')
        record_email_open(tracking_id2, 'Firefox', '********')
        
        # Call the function
        result = get_tracking_summary()
        
        # Check the result
        self.assertEqual(result['total_emails'], 2)
        self.assertEqual(result['total_opens'], 3)
        self.assertIn(tracking_id1, result['emails'])
        self.assertIn(tracking_id2, result['emails'])
        self.assertEqual(result['emails'][tracking_id1]['recipient'], recipient1)
        self.assertEqual(result['emails'][tracking_id1]['subject'], subject1)
        self.assertEqual(result['emails'][tracking_id1]['opens'], 2)
        self.assertEqual(result['emails'][tracking_id2]['recipient'], recipient2)
        self.assertEqual(result['emails'][tracking_id2]['subject'], subject2)
        self.assertEqual(result['emails'][tracking_id2]['opens'], 1)

if __name__ == '__main__':
    unittest.main()
