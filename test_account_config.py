#!/usr/bin/env python3
"""
Test script to verify account-specific email configuration functionality
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv(override=True)

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.config import get_account_app_password, get_account_config
from app.database.supabase_client import db

def test_account_app_password():
    """Test the account app password retrieval functionality."""
    print("=== Testing Account App Password Retrieval ===")
    
    # Test with a sample email address
    test_email = "<EMAIL>"
    print(f"Testing with email: {test_email}")
    
    # Expected environment variable name
    expected_env_var = "MATEUSZ_PANELJAZDY_PL_APP_PASSWORD"
    print(f"Expected environment variable: {expected_env_var}")
    
    # Check if the environment variable exists
    env_value = os.getenv(expected_env_var)
    if env_value:
        print(f"✓ Environment variable found: {expected_env_var} = {'*' * len(env_value)}")
    else:
        print(f"✗ Environment variable not found: {expected_env_var}")
    
    # Test the function
    app_password = get_account_app_password(test_email)
    if app_password:
        print(f"✓ get_account_app_password returned: {'*' * len(app_password)}")
    else:
        print("✗ get_account_app_password returned None")
    
    print()

def test_account_config():
    """Test the account configuration generation."""
    print("=== Testing Account Configuration Generation ===")
    
    # Sample account data (as would come from database)
    sample_account_data = {
        'id': 'test-account-id',
        'email': '<EMAIL>',
        'display_name': 'Mateusz',
        'smtp_server': 'smtp.zoho.eu',
        'smtp_port': 465,
        'use_ssl': True,
        'daily_limit': 10,
        'active': True
    }
    
    print(f"Sample account data: {sample_account_data['email']}")
    
    try:
        config = get_account_config(sample_account_data)
        print("✓ Account configuration generated successfully:")
        for key, value in config.items():
            if key == 'app_password' and value:
                print(f"  {key}: {'*' * len(value)}")
            else:
                print(f"  {key}: {value}")
    except Exception as e:
        print(f"✗ Error generating account configuration: {str(e)}")
    
    print()

def test_database_account_retrieval():
    """Test retrieving account data from the database."""
    print("=== Testing Database Account Retrieval ===")
    
    try:
        # Get all email accounts
        accounts = db.get_email_accounts()
        print(f"Found {len(accounts)} email accounts in database:")
        
        for account in accounts:
            print(f"  - {account.get('email')} (ID: {account.get('id')})")
            
            # Test getting account by ID
            account_id = account.get('id')
            if account_id:
                retrieved_account = db.get_email_account_by_id(account_id)
                if retrieved_account:
                    print(f"    ✓ Successfully retrieved account by ID")
                    
                    # Test generating config for this account
                    try:
                        config = get_account_config(retrieved_account)
                        has_password = config.get('app_password') is not None
                        print(f"    ✓ Generated config (has password: {has_password})")
                    except Exception as e:
                        print(f"    ✗ Error generating config: {str(e)}")
                else:
                    print(f"    ✗ Failed to retrieve account by ID")
        
    except Exception as e:
        print(f"✗ Error accessing database: {str(e)}")
    
    print()

def main():
    """Main test function."""
    print("Testing Account-Specific Email Configuration")
    print("=" * 50)
    
    test_account_app_password()
    test_account_config()
    test_database_account_retrieval()
    
    print("Test completed!")

if __name__ == "__main__":
    main()
