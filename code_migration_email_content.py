#!/usr/bin/env python3
"""
Code migration script to update the application to work with the new database schema
where email subject and body are stored in a separate email_content table.

This script updates the SupabaseClient class to handle the new schema.
"""
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from app.database.supabase_client import db
    from app.utils.logger import setup_logger
except ImportError:
    print("Error: Could not import required modules from the app package.")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Set up logger
logger = setup_logger('code_migration')

def update_supabase_client():
    """
    Update the SupabaseClient class to handle the new database schema.
    """
    # Path to the supabase_client.py file
    file_path = os.path.join('app', 'database', 'supabase_client.py')

    # Read the current file content
    with open(file_path, 'r') as f:
        content = f.read()

    # Update the queue_email method
    old_queue_email = """    def queue_email(self, account_id: str, recipient_id: str, subject: str,
                   body: str, scheduled_time: datetime.datetime) -> Dict[str, Any]:
        \"\"\"
        Add an email to the queue.

        Args:
            account_id (str): Email account ID
            recipient_id (str): Recipient ID
            subject (str): Email subject
            body (str): Email body
            scheduled_time (datetime.datetime): Scheduled send time

        Returns:
            Dict[str, Any]: Queued email
        \"\"\"
        email_data = {
            'account_id': account_id,
            'recipient_id': recipient_id,
            'subject': subject,
            'body': body,
            'scheduled_time': scheduled_time.isoformat(),
            'status': 'scheduled'
        }

        response = self.client.table('email_queue').insert(email_data).execute()
        return self._handle_response(response)"""

    new_queue_email = """    def queue_email(self, account_id: str, recipient_id: str, subject: str,
                   body: str, scheduled_time: datetime.datetime, follow_up: int = 0) -> Dict[str, Any]:
        \"\"\"
        Add an email to the queue.

        Args:
            account_id (str): Email account ID
            recipient_id (str): Recipient ID
            subject (str): Email subject (will be replaced with template subject)
            body (str): Email body (will be replaced with template body)
            scheduled_time (datetime.datetime): Scheduled send time
            follow_up (int, optional): Follow-up number (0-4). Defaults to 0.

        Returns:
            Dict[str, Any]: Queued email
        \"\"\"
        # Determine which template to use based on follow_up number
        template_id = None

        # Map follow_up number to template ID
        if follow_up == 0:
            template_id = '********-0000-0000-0000-********0001'  # Initial email
        elif follow_up == 1:
            template_id = '********-0000-0000-0000-********0002'  # 1st follow-up
        elif follow_up == 2:
            template_id = '********-0000-0000-0000-********0003'  # 2nd follow-up
        elif follow_up == 3:
            template_id = '********-0000-0000-0000-************'  # 3rd follow-up
        elif follow_up == 4:
            template_id = '********-0000-0000-0000-********0005'  # 4th follow-up (final)
        else:
            # Default to initial email template for unexpected follow_up values
            template_id = '********-0000-0000-0000-********0001'
            logger.warning(f"Unexpected follow_up value: {follow_up}, using initial email template")

        # Get the template content for logging purposes
        response = self.client.table('email_content').select('*').eq('id', template_id).execute()
        template = self._handle_response(response)

        if template and len(template) > 0:
            template_subject = template[0]['subject']
            template_body = template[0]['body']
            logger.debug(f"Using template {template_id} with subject: {template_subject}")
        else:
            logger.error(f"Template with ID {template_id} not found")
            return None

        # Create the email queue entry with the template ID
        email_data = {
            'account_id': account_id,
            'recipient_id': recipient_id,
            'content_id': template_id,
            'scheduled_time': scheduled_time.isoformat(),
            'status': 'scheduled',
            'follow_up': follow_up
        }

        response = self.client.table('email_queue').insert(email_data).execute()
        result = self._handle_response(response)

        # If successful, add subject and body to the result for backward compatibility
        if result and len(result) > 0:
            result[0]['subject'] = template_subject
            result[0]['body'] = template_body

        return result"""

    # Add the get_email_content method
    new_get_email_content = """    def get_email_content(self, email_id: str) -> Dict[str, Any]:
        \"\"\"
        Get the subject and body for an email.

        Args:
            email_id (str): Email ID

        Returns:
            Dict[str, Any]: Email content with subject and body
        \"\"\"
        try:
            # Call the database function to get email content
            response = self.client.rpc(
                'get_email_content',
                {'p_email_id': email_id}
            ).execute()

            result = self._handle_response(response)

            if result and len(result) > 0:
                return result[0]
            else:
                # Fallback: try to get content_id from email_queue and then get content
                email = self.get_email_by_id(email_id)
                if email and email.get('content_id'):
                    content_id = email.get('content_id')
                    response = self.client.table('email_content').select('*').eq('id', content_id).execute()
                    content = self._handle_response(response)

                    if content and len(content) > 0:
                        return content[0]

            return None

        except Exception as e:
            logger.error(f"Error getting email content for {email_id}: {str(e)}")
            return None"""

    # Update the get_email_by_id method
    old_get_email_by_id = """    def get_email_by_id(self, email_id: str) -> Dict[str, Any]:
        \"\"\"
        Get an email from the queue by ID.

        Args:
            email_id (str): Email ID

        Returns:
            Dict[str, Any]: Email data
        \"\"\"
        response = self.client.table('email_queue').select('*').eq('id', email_id).execute()
        result = self._handle_response(response)

        if result and len(result) > 0:
            return result[0]

        return None"""

    new_get_email_by_id = """    def get_email_by_id(self, email_id: str) -> Dict[str, Any]:
        \"\"\"
        Get an email from the queue by ID.

        Args:
            email_id (str): Email ID

        Returns:
            Dict[str, Any]: Email data
        \"\"\"
        # Join with email_content to get subject and body
        response = self.client.from_('email_queue').select(
            'email_queue.id, email_queue.account_id, email_queue.recipient_id, ' +
            'email_queue.tracking_id, email_queue.scheduled_time, email_queue.status, ' +
            'email_queue.attempts, email_queue.max_attempts, email_queue.last_attempt_time, ' +
            'email_queue.sent_time, email_queue.error_message, email_queue.is_follow_up, ' +
            'email_queue.follow_up, email_queue.previous_email_id, email_queue.message_id, ' +
            'email_queue.reply_message_id, email_queue.reply_references, ' +
            'email_queue.reply_in_reply_to, email_queue.content_id, email_queue.created_at, ' +
            'email_queue.updated_at, email_content:content_id(subject, body)'
        ).eq('email_queue.id', email_id).execute()

        result = self._handle_response(response)

        if result and len(result) > 0:
            email = result[0]

            # Extract subject and body from the nested content object
            if email.get('email_content') and len(email['email_content']) > 0:
                email['subject'] = email['email_content'][0]['subject']
                email['body'] = email['email_content'][0]['body']

                # Remove the nested content object to maintain backward compatibility
                del email['email_content']

            return email

        return None"""

    # Make the replacements
    content = content.replace(old_queue_email, new_queue_email)
    content = content.replace(old_get_email_by_id, new_get_email_by_id)

    # Add the get_email_content method before the "# Email Tracking" section
    content = content.replace("    # Email Tracking", new_get_email_content + "\n\n    # Email Tracking")

    # Write the updated content back to the file
    with open(file_path, 'w') as f:
        f.write(content)

    logger.info(f"Updated {file_path}")

    return True

def add_get_template_method():
    """
    Add a new method to get the appropriate template ID based on follow-up number.
    """
    # Path to the supabase_client.py file
    file_path = os.path.join('app', 'database', 'supabase_client.py')

    # Read the current file content
    with open(file_path, 'r') as f:
        content = f.read()

    # Add the get_template_id method
    new_get_template_id = """    def get_template_id(self, follow_up: int) -> str:
        \"\"\"
        Get the appropriate template ID based on follow-up number.

        Args:
            follow_up (int): Follow-up number (0-4)

        Returns:
            str: Template ID
        \"\"\"
        # Map follow_up number to template ID
        if follow_up == 0:
            return '********-0000-0000-0000-********0001'  # Initial email
        elif follow_up == 1:
            return '********-0000-0000-0000-********0002'  # 1st follow-up
        elif follow_up == 2:
            return '********-0000-0000-0000-********0003'  # 2nd follow-up
        elif follow_up == 3:
            return '********-0000-0000-0000-************'  # 3rd follow-up
        elif follow_up == 4:
            return '********-0000-0000-0000-********0005'  # 4th follow-up (final)
        else:
            # Default to initial email template for unexpected follow_up values
            logger.warning(f"Unexpected follow_up value: {follow_up}, using initial email template")
            return '********-0000-0000-0000-********0001'"""

    # Add the method before the "# Email Tracking" section
    if "    def get_template_id(self, follow_up: int)" not in content:
        content = content.replace("    # Email Tracking", new_get_template_id + "\n\n    # Email Tracking")

        # Write the updated content back to the file
        with open(file_path, 'w') as f:
            f.write(content)

        logger.info(f"Added get_template_id method to {file_path}")
    else:
        logger.info("get_template_id method already exists, skipping")

    return True

def update_schedule_follow_up():
    """
    Update the schedule_follow_up method to use the template system.
    """
    # Path to the supabase_client.py file
    file_path = os.path.join('app', 'database', 'supabase_client.py')

    # Read the current file content
    with open(file_path, 'r') as f:
        content = f.read()

    # Find the schedule_follow_up method
    if "def schedule_follow_up(self, email_id: str, follow_up_days: int," in content:
        # Update the method to use the template system
        old_method_start = "    def schedule_follow_up(self, email_id: str, follow_up_days: int,"
        old_method_end = "        return self._handle_response(response)"

        # Find the start and end positions of the method
        start_pos = content.find(old_method_start)
        if start_pos == -1:
            logger.error("Could not find schedule_follow_up method")
            return False

        # Find the end of the method
        end_pos = content.find(old_method_end, start_pos)
        if end_pos == -1:
            logger.error("Could not find end of schedule_follow_up method")
            return False

        # Get the full method text
        end_pos += len(old_method_end)
        old_method = content[start_pos:end_pos]

        # Create the new method
        new_method = """    def schedule_follow_up(self, email_id: str, follow_up_days: int,
                          follow_up_subject: str = None,
                          follow_up_body: str = None) -> Dict[str, Any]:
        \"\"\"
        Schedule a follow-up email for a sent email.

        Args:
            email_id (str): ID of the original email
            follow_up_days (int): Number of days to wait before sending the follow-up
            follow_up_subject (str, optional): Custom subject for the follow-up (ignored, using template)
            follow_up_body (str, optional): Custom body for the follow-up (ignored, using template)

        Returns:
            Dict[str, Any]: The scheduled follow-up email
        \"\"\"
        # Get the original email to determine the follow-up number
        original_email = self.get_email_by_id(email_id)
        if not original_email:
            logger.error(f"Original email with ID {email_id} not found")
            return None

        # Calculate the follow-up number (original email follow_up + 1)
        current_follow_up = original_email.get('follow_up', 0)
        next_follow_up = current_follow_up + 1

        # Check if we've reached the maximum follow-up number (4)
        if next_follow_up > 4:
            logger.warning(f"Maximum follow-up number reached for email {email_id}")
            return None

        # Get the appropriate template ID for this follow-up
        template_id = self.get_template_id(next_follow_up)

        # Call the database function to schedule the follow-up
        params = {
            'email_id': email_id,
            'follow_up_days': follow_up_days
        }

        response = self.client.rpc('schedule_follow_up_email', params).execute()
        result = self._handle_response(response)

        if result and len(result) > 0:
            # Update the content_id to use the appropriate template
            follow_up_id = result[0].get('id')
            if follow_up_id:
                self.update_email(follow_up_id, {
                    'content_id': template_id,
                    'follow_up': next_follow_up
                })

                # Get the updated email with template content
                return self.get_email_by_id(follow_up_id)

        return result"""

        # Replace the old method with the new one
        content = content.replace(old_method, new_method)

        # Write the updated content back to the file
        with open(file_path, 'w') as f:
            f.write(content)

        logger.info(f"Updated schedule_follow_up method in {file_path}")
    else:
        logger.warning("Could not find schedule_follow_up method, skipping")

    return True

def update_setup_test_email():
    """
    Update the setup_test_email function in queue.py to use the template system.
    """
    # Path to the queue.py file
    file_path = os.path.join('app', 'server', 'queue.py')

    # Read the current file content
    with open(file_path, 'r') as f:
        content = f.read()

    # Find the setup_test_email function
    if "def setup_test_email():" in content:
        # Find the part where it creates the email data
        old_code = """        # Create test email content
        subject = f"Test Email - {now.strftime('%Y-%m-%d %H:%M:%S')}"
        body = f\"\"\"
        <p>This is a test email sent in test mode at {now.strftime('%Y-%m-%d %H:%M:%S')}.</p>
        <p>This email will be followed by 5 follow-up emails at 1-minute intervals.</p>
        <p>Follow-up schedule in test mode:</p>
        <ul>
            <li>1st follow-up: 1 minute after this email</li>
            <li>2nd follow-up: 1 minute after 1st follow-up</li>
            <li>3rd follow-up: 1 minute after 2nd follow-up</li>
            <li>4th follow-up: 1 minute after 3rd follow-up</li>
            <li>5th follow-up: 1 minute after 4th follow-up</li>
        </ul>
        <p>Each follow-up will include the content of the previous email in a conversation thread format.</p>
        <p>Best regards,<br>{EMAIL_CONFIG['sender_name']}</p>
        \"\"\"

        # Add the email to the queue
        email_data = {
            'account_id': account_id,
            'recipient_id': recipient_id,
            'subject': subject,
            'body': body,
            'scheduled_time': scheduled_time.isoformat(),
            'status': 'scheduled'
        }"""

        new_code = """        # Get the template for the initial email (follow_up = 0)
        template_id = db.get_template_id(0)

        # Get the template content for logging
        response = db.client.table('email_content').select('*').eq('id', template_id).execute()
        template = db._handle_response(response)

        if not template or len(template) == 0:
            logger.error(f"Template with ID {template_id} not found")
            return

        subject = template[0]['subject']
        body = template[0]['body']

        logger.info(f"Using template {template_id} with subject: {subject}")

        # Add the email to the queue
        email_data = {
            'account_id': account_id,
            'recipient_id': recipient_id,
            'content_id': template_id,
            'scheduled_time': scheduled_time.isoformat(),
            'status': 'scheduled',
            'follow_up': 0
        }"""

        # Replace the old code with the new code
        content = content.replace(old_code, new_code)

        # Write the updated content back to the file
        with open(file_path, 'w') as f:
            f.write(content)

        logger.info(f"Updated setup_test_email function in {file_path}")
    else:
        logger.warning("Could not find setup_test_email function, skipping")

    return True

def main():
    """Main function."""
    logger.info("Starting code migration for email content optimization")

    # Update the SupabaseClient class
    if update_supabase_client():
        logger.info("Successfully updated SupabaseClient class")
    else:
        logger.error("Failed to update SupabaseClient class")
        return False

    # Add the get_template_id method
    if add_get_template_method():
        logger.info("Successfully added get_template_id method")
    else:
        logger.error("Failed to add get_template_id method")
        return False

    # Update the schedule_follow_up method
    if update_schedule_follow_up():
        logger.info("Successfully updated schedule_follow_up method")
    else:
        logger.error("Failed to update schedule_follow_up method")
        return False

    # Update the setup_test_email function
    if update_setup_test_email():
        logger.info("Successfully updated setup_test_email function")
    else:
        logger.error("Failed to update setup_test_email function")
        return False

    logger.info("Code migration completed successfully")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
