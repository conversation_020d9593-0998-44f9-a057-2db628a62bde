#!/usr/bin/env python3
"""
Test script to send an email to a recipient from the mock database
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(override=True)

# Add the current directory to the path so we can import the app modules
sys.path.append('.')

from app.config import EMAIL_CONFIG
from app.email.sender import send_email
from app.utils.logger import setup_logger
from app.database.supabase_client import SupabaseClient

# Set up logger
logger = setup_logger()

def main():
    """Main function to send a test email to a recipient from the mock database."""
    # Print email configuration
    print("Email configuration:")
    print(f"sender_email: {EMAIL_CONFIG['sender_email']}")
    print(f"app_password: {'*' * len(EMAIL_CONFIG['app_password']) if EMAIL_CONFIG['app_password'] else 'Not set'}")
    print(f"smtp_server: {EMAIL_CONFIG['smtp_server']}")
    print(f"smtp_port: {EMAIL_CONFIG['smtp_port']}")
    
    # Create a mock database
    db = SupabaseClient()
    db.client = None
    db._mock_db = {
        'email_accounts': [],
        'recipients': [],
        'email_queue': [],
        'email_tracking': [],
        'daily_schedules': [],
        'link_tracking': [],
        'server_status': []
    }
    
    # Create a test recipient
    recipient = db.create_recipient(
        email="<EMAIL>",
        first_name="MatHat",
        last_name="Contact",
        company="MatHat"
    )
    print(f"\nCreated recipient: {recipient.get('email')}, ID: {recipient.get('id')}")
    
    # Define email content
    subject = "Test Email from ColdMails"
    body = """
    <html>
    <body>
        <h1>Test Email</h1>
        <p>This is a test email sent from the ColdMails application.</p>
        <p>If you're seeing this, the email sending functionality is working correctly.</p>
        <p>Best regards,<br>ColdMails Test</p>
    </body>
    </html>
    """
    
    # Check if we have a valid app password
    app_password = EMAIL_CONFIG['app_password']
    if not app_password or app_password == 'your-actual-app-password-here':
        print("\nNo valid app password found in .env file. Cannot send email.")
        print("Please update the ZOHO_APP_PASSWORD in your .env file with a valid app password.")
        print("\nTo send an email with a command-line password, run:")
        print(f"python main.py YOUR_APP_PASSWORD --email {EMAIL_CONFIG['sender_email']}")
        return
    
    # Send the email
    print(f"\nSending test email to {recipient.get('email')}...")
    success, result = send_email(recipient.get('email'), subject, body)
    
    if success:
        print(f"Email sent successfully! Tracking ID: {result}")
        
        # Record the email in the database
        print("\nRecording email in the database...")
        email_id = "test-email-1"
        tracking_id = result
        db._mock_db['email_queue'].append({
            'id': email_id,
            'tracking_id': tracking_id,
            'recipient_id': recipient.get('id'),
            'subject': subject,
            'body': body,
            'status': 'sent',
            'sent_time': '2025-05-15T18:00:00Z'
        })
        
        # Mark the email for follow-up
        print("\nMarking email for follow-up...")
        db.mark_for_follow_up(email_id, days=3)
        
        # Get emails needing follow-up
        print("\nGetting emails needing follow-up...")
        follow_ups = db.get_emails_needing_follow_up()
        print(f"Found {len(follow_ups)} emails needing follow-up:")
        for f in follow_ups:
            print(f"- Email ID: {f.get('id')}, Subject: {f.get('subject')}")
    else:
        print(f"Failed to send email: {result}")

if __name__ == "__main__":
    main()
