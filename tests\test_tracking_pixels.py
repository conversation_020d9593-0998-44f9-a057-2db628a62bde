#!/usr/bin/env python3
"""
Tests for the tracking pixels module
"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import base64
import time
import uuid

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase
from app.tracking.pixels import (
    generate_tracking_id,
    get_tracking_pixel_url,
    get_tracking_pixel_data,
    add_tracking_pixel,
    generate_test_pixels,
    TRACKING_PIXELS
)
from app.config import SERVER_CONFIG

class TestTrackingPixels(BaseTestCase):
    """Test cases for the tracking pixels module."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Mock uuid.uuid4
        self.uuid_patcher = patch('uuid.uuid4')
        self.mock_uuid = self.uuid_patcher.start()
        self.mock_uuid.return_value = uuid.UUID('00000000-0000-0000-0000-000000000011')
        
        # Mock time.time
        self.time_patcher = patch('time.time')
        self.mock_time = self.time_patcher.start()
        self.mock_time.return_value = 1609459200  # 2021-01-01 00:00:00
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        self.uuid_patcher.stop()
        self.time_patcher.stop()
    
    def test_generate_tracking_id(self):
        """Test generating a tracking ID."""
        tracking_id = generate_tracking_id()
        self.assertEqual(tracking_id, '00000000-0000-0000-0000-000000000011')
        self.mock_uuid.assert_called_once()
    
    def test_get_tracking_pixel_url(self):
        """Test getting a tracking pixel URL."""
        # Test with default parameters
        tracking_id = '00000000-0000-0000-0000-000000000011'
        url = get_tracking_pixel_url(tracking_id)
        expected_url = f"{SERVER_CONFIG['base_url']}/track/{tracking_id}/transparent.gif?t=1609459200"
        self.assertEqual(url, expected_url)
        
        # Test with custom pixel type
        url = get_tracking_pixel_url(tracking_id, 'red')
        expected_url = f"{SERVER_CONFIG['base_url']}/track/{tracking_id}/pixel.gif?t=1609459200"
        self.assertEqual(url, expected_url)
        
        # Test with custom timestamp
        url = get_tracking_pixel_url(tracking_id, 'blue', 1234567890)
        expected_url = f"{SERVER_CONFIG['base_url']}/track/{tracking_id}/blue.gif?t=1234567890"
        self.assertEqual(url, expected_url)
    
    def test_get_tracking_pixel_data(self):
        """Test getting tracking pixel data."""
        # Test transparent pixel (default)
        data = get_tracking_pixel_data()
        expected_data = base64.b64decode(TRACKING_PIXELS['transparent'])
        self.assertEqual(data, expected_data)
        
        # Test red pixel
        data = get_tracking_pixel_data('red')
        expected_data = base64.b64decode(TRACKING_PIXELS['red'])
        self.assertEqual(data, expected_data)
        
        # Test blue pixel
        data = get_tracking_pixel_data('blue')
        expected_data = base64.b64decode(TRACKING_PIXELS['blue'])
        self.assertEqual(data, expected_data)
        
        # Test logo pixel
        data = get_tracking_pixel_data('logo')
        expected_data = base64.b64decode(TRACKING_PIXELS['logo'])
        self.assertEqual(data, expected_data)
        
        # Test invalid pixel type (should return transparent)
        data = get_tracking_pixel_data('invalid')
        expected_data = base64.b64decode(TRACKING_PIXELS['transparent'])
        self.assertEqual(data, expected_data)
    
    def test_add_tracking_pixel(self):
        """Test adding a tracking pixel to HTML content."""
        # Test with HTML that has a closing body tag
        tracking_id = '00000000-0000-0000-0000-000000000011'
        html = '<html><body><p>Test</p></body></html>'
        result = add_tracking_pixel(html, tracking_id)
        pixel_url = get_tracking_pixel_url(tracking_id, 'transparent', 1609459200)
        expected_html = f'<html><body><p>Test</p><img src="{pixel_url}" width="1" height="1" alt="" style="display:none;width:1px;height:1px;"/></body></html>'
        self.assertEqual(result, expected_html)
        
        # Test with HTML that has a closing html tag but no body tag
        html = '<html><p>Test</p></html>'
        result = add_tracking_pixel(html, tracking_id)
        expected_html = f'<html><p>Test</p><img src="{pixel_url}" width="1" height="1" alt="" style="display:none;width:1px;height:1px;"/></html>'
        self.assertEqual(result, expected_html)
        
        # Test with HTML that has no closing tags
        html = '<p>Test</p>'
        result = add_tracking_pixel(html, tracking_id)
        expected_html = f'<p>Test</p><img src="{pixel_url}" width="1" height="1" alt="" style="display:none;width:1px;height:1px;"/>'
        self.assertEqual(result, expected_html)
    
    def test_generate_test_pixels(self):
        """Test generating test tracking pixels."""
        # Test with default parameters
        result = generate_test_pixels()
        tracking_id = '00000000-0000-0000-0000-000000000011'
        self.assertEqual(result['tracking_id'], tracking_id)
        self.assertEqual(result['red_url'], get_tracking_pixel_url(tracking_id, 'red', 1609459200))
        self.assertEqual(result['blue_url'], get_tracking_pixel_url(tracking_id, 'blue', 1609459200))
        self.assertEqual(result['logo_url'], get_tracking_pixel_url(tracking_id, 'logo', 1609459200))
        self.assertEqual(result['transparent_url'], get_tracking_pixel_url(tracking_id, 'transparent', 1609459200))
        self.assertEqual(result['check_url'], f"{SERVER_CONFIG['base_url']}/tracking/{tracking_id}")
        
        # Test with custom tracking ID
        custom_id = '00000000-0000-0000-0000-000000000012'
        result = generate_test_pixels(custom_id)
        self.assertEqual(result['tracking_id'], custom_id)
        self.assertEqual(result['red_url'], get_tracking_pixel_url(custom_id, 'red', 1609459200))
        self.assertEqual(result['blue_url'], get_tracking_pixel_url(custom_id, 'blue', 1609459200))
        self.assertEqual(result['logo_url'], get_tracking_pixel_url(custom_id, 'logo', 1609459200))
        self.assertEqual(result['transparent_url'], get_tracking_pixel_url(custom_id, 'transparent', 1609459200))
        self.assertEqual(result['check_url'], f"{SERVER_CONFIG['base_url']}/tracking/{custom_id}")

if __name__ == '__main__':
    unittest.main()
