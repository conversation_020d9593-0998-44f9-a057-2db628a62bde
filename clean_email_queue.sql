-- <PERSON><PERSON>t to remove all records from the email queue with status: failed, scheduled, or sending
-- Also removes corresponding records in related tables (email_tracking and link_tracking)

-- Start a transaction to ensure all operations are atomic
BEGIN;

-- First, let's create a temporary table to store the IDs of emails we want to delete
CREATE TEMP TABLE emails_to_delete AS
SELECT id, tracking_id
FROM email_queue
WHERE status IN ('failed', 'scheduled', 'sending');

-- Log the number of emails that will be deleted
DO $$
DECLARE
    email_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO email_count FROM emails_to_delete;
    RAISE NOTICE 'Found % emails with status failed, scheduled, or sending to delete', email_count;
END $$;

-- Delete records from link_tracking table that reference the emails to be deleted
DELETE FROM link_tracking
WHERE email_id IN (SELECT id FROM emails_to_delete)
OR tracking_id IN (SELECT tracking_id FROM emails_to_delete);

-- Log the number of link_tracking records deleted
DO $$
DECLARE
    deleted_count INTEGER;
BEGIN
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % records from link_tracking table', deleted_count;
END $$;

-- Delete records from email_tracking table that reference the emails to be deleted
DELETE FROM email_tracking
WHERE email_id IN (SELECT id FROM emails_to_delete)
OR tracking_id IN (SELECT tracking_id FROM emails_to_delete);

-- Log the number of email_tracking records deleted
DO $$
DECLARE
    deleted_count INTEGER;
BEGIN
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % records from email_tracking table', deleted_count;
END $$;

-- Finally, delete the email_queue records
DELETE FROM email_queue
WHERE id IN (SELECT id FROM emails_to_delete);

-- Log the number of email_queue records deleted
DO $$
DECLARE
    deleted_count INTEGER;
BEGIN
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % records from email_queue table', deleted_count;
END $$;

-- Drop the temporary table
DROP TABLE emails_to_delete;

-- Commit the transaction
COMMIT;

-- Output a completion message
DO $$
BEGIN
    RAISE NOTICE 'Email queue cleanup completed successfully';
END $$;
