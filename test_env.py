#!/usr/bin/env python3
"""
Test script to verify that environment variables are loaded correctly
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(override=True)

# Print environment variables
print("Environment variables:")
print(f"ZOHO_EMAIL: {os.getenv('ZOHO_EMAIL', 'Not set')}")
print(f"ZOHO_APP_PASSWORD: {'*' * len(os.getenv('ZOHO_APP_PASSWORD', '')) if os.getenv('ZOHO_APP_PASSWORD') else 'Not set'}")
print(f"SUPABASE_URL: {os.getenv('SUPABASE_URL', 'Not set')}")
print(f"SUPABASE_KEY: {os.getenv('SUPABASE_KEY', 'Not set')[:10]}... (truncated)")

# Import the config to see if it's loaded correctly
from app.config import EMAIL_CONFIG

print("\nEmail configuration:")
print(f"sender_email: {EMAIL_CONFIG['sender_email']}")
print(f"app_password: {'*' * len(EMAIL_CONFIG['app_password']) if EMAIL_CONFIG['app_password'] else 'Not set'}")
print(f"smtp_server: {EMAIL_CONFIG['smtp_server']}")
print(f"smtp_port: {EMAIL_CONFIG['smtp_port']}")
