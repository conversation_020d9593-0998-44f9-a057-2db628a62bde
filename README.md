# Cold Email Server

A lightweight, customizable cold email server with email tracking capabilities, built with Python.

## Features

- **Email Sending**: Send HTML emails using Zoho Mail
- **Email Tracking**: Track when recipients open your emails using tracking pixels
- **Automated Follow-ups**: Schedule and send follow-up emails automatically
- **Tracking Statistics**: View detailed statistics about email opens
- **Simple API**: RESTful API for sending emails and retrieving tracking data
- **Minimal Dependencies**: Built with standard Python libraries and minimal external dependencies

## Project Structure

```
ColdMails/
├── app/                    # Main application package
│   ├── __init__.py
│   ├── config.py           # Configuration settings
│   ├── server.py           # HTTP server implementation
│   ├── database/           # Database functionality
│   │   ├── __init__.py
│   │   └── supabase_client.py # Supabase database client
│   ├── email/              # Email functionality
│   │   ├── __init__.py
│   │   ├── sender.py       # Email sending logic
│   │   └── follow_up.py    # Follow-up email functionality
│   ├── tracking/           # Tracking functionality
│   │   ├── __init__.py
│   │   ├── database.py     # In-memory database (can be replaced with Supabase)
│   │   ├── pixels.py       # Tracking pixel generation
│   │   └── handlers.py     # Tracking endpoint handlers
│   └── utils/              # Utility functions
│       ├── __init__.py
│       ├── logger.py       # Logging configuration
│       └── keep_alive.py   # Keep-alive functionality for Render
├── main.py                 # Entry point
├── requirements.txt        # Dependencies
├── supabase_setup.sql      # Supabase database setup script
├── examples/               # Example scripts
│   └── supabase_example.py # Example of using Supabase
└── tests/                  # Test scripts
    ├── __init__.py
    └── test_email.py       # Email testing script
```

## Requirements

- Python 3.6+
- Zoho Mail account with app password
- Public URL for tracking (e.g., using ngrok)

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/cold-email-server.git
   cd cold-email-server
   ```

2. Set up a virtual environment (Windows 10):
   ```
   # Install virtualenv if you don't have it
   pip install virtualenv

   # Create a virtual environment
   python -m venv venv

   # Activate the virtual environment
   venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Set up a Zoho Mail account and generate an app password.

## Configuration

The server can be configured through command-line arguments or by modifying the `app/config.py` file.

### Email Configuration

- `smtp_server`: SMTP server address (default: smtp.zoho.eu)
- `smtp_port`: SMTP port (default: 465 for SSL)
- `sender_email`: Your Zoho email address
- `app_password`: Your Zoho app password (provided via command line)
- `use_tls`: Whether to use TLS instead of SSL (default: False)
- `track_opens`: Whether to track email opens (default: True)

### Server Configuration

- `host`: Host to bind to (default: localhost)
- `port`: Port to bind to (default: 8000)
- `base_url`: Public URL for tracking (e.g., ngrok URL)

## Usage

### Starting the Server

Start the server with your Zoho app password:

```
python main.py YOUR_APP_PASSWORD
```

Additional options:
```
python main.py --help
```

### Testing Authentication

Test your Zoho authentication:

```
python main.py --test-auth YOUR_APP_PASSWORD
```

### Sending Emails

Send emails using the HTTP API:

```
POST /send-email
Content-Type: application/json

{
  "recipient": "<EMAIL>",
  "subject": "Your Subject",
  "body": "<html><body><h1>Hello!</h1><p>This is a test email.</p></body></html>",
  "email_id": "optional-database-id",
  "needs_follow_up": true,
  "follow_up_days": 3
}
```

Response:
```json
{
  "status": "success",
  "tracking_id": "unique-tracking-id",
  "tracking_enabled": true,
  "follow_up": true
}
```

The `email_id`, `needs_follow_up`, and `follow_up_days` fields are optional:
- `email_id`: ID of the email in the database (if using Supabase)
- `needs_follow_up`: Whether to schedule a follow-up email (default: false)
- `follow_up_days`: Number of days to wait before sending the follow-up (default: 3)

### Tracking Endpoints

- `GET /tracking`: Get summary of all tracked emails
- `GET /tracking/{tracking_id}`: Get details for a specific email
- `GET /track/{tracking_id}/pixel.gif`: Tracking pixel (invisible)

### Follow-up Endpoints

- `GET /process-follow-ups`: Manually trigger processing of follow-up emails

### Testing

Use the test script to send a test email:

```
python tests/test_email.py
```

## How It Works

### Email Sending

1. The server receives a request to send an email via the `/send-email` endpoint.
2. It connects to Zoho Mail's SMTP server using SSL.
3. It authenticates using your email and app password.
4. If tracking is enabled, it adds an invisible tracking pixel to the email.
5. It sends the email and records it in the tracking database.

### Email Tracking

1. When a recipient opens an email, their email client loads the tracking pixel.
2. This sends a request to the server's `/track/{tracking_id}/pixel.gif` endpoint.
3. The server records the open event with timestamp, user agent, and IP address.
4. You can view tracking statistics via the `/tracking` endpoints.

### Follow-up Emails

1. When sending an email, you can mark it for follow-up by setting `needs_follow_up: true`.
2. The server will automatically schedule follow-up emails based on the specified delay (default: 3 days).
3. A background process checks for emails that need follow-ups every hour.
4. Follow-up emails are automatically added to the queue and sent at the scheduled time.
5. The system supports up to 2 follow-up emails per initial email (3 emails total in the sequence).
6. Each follow-up email includes a reference to the original email and increments the follow-up sequence number.

### Tracking Pixels

The server uses invisible 1x1 pixel GIFs for tracking. These are embedded in the HTML email and loaded when the recipient opens the email. The server supports different types of tracking pixels:

- `transparent.gif`: Invisible pixel (default)
- `pixel.gif`: Red pixel (for testing)
- `blue.gif`: Blue pixel (for testing)
- `logo.gif`: Small logo (for testing)

## Deployment

### Local Development with ngrok

For local development, you can use ngrok to expose your local server to the internet:

1. Start the server:
   ```
   python main.py YOUR_APP_PASSWORD
   ```

2. In another terminal, start ngrok:
   ```
   ngrok http 8000
   ```

3. Update the base URL:
   ```
   python main.py YOUR_APP_PASSWORD --base-url https://your-ngrok-url.ngrok-free.app
   ```

### Production Deployment

For production, you can deploy to:

1. **Render** (recommended for free tier):
   - Sign up at [render.com](https://render.com)
   - Create a new Web Service
   - Connect your GitHub repository
   - Set the build command: `pip install -r requirements.txt`
   - Set the start command: `python main.py $ZOHO_APP_PASSWORD`
   - Add environment variables for your Zoho credentials
   - The server includes a built-in keep-alive mechanism to prevent Render's free tier from sleeping after 15 minutes of inactivity

2. **VPS** (Digital Ocean, Linode, etc.):
   - Set up a small VPS
   - Clone your repository
   - Install dependencies
   - Set up a systemd service to run the server
   - Use a reverse proxy like Nginx

### Keep-Alive Mechanism

The server includes a built-in ping-pong mechanism to prevent it from sleeping on Render's free tier:

- Every ~14 minutes, the server makes a request to itself
- This keeps the server active continuously without requiring external services
- The mechanism uses two endpoints: `/ping` and `/pong` that call each other in sequence
- You can disable this with the `--no-keep-alive` flag, but it's not recommended for Render deployment

This ensures that:
1. Your server remains awake to send scheduled emails
2. Tracking pixels work reliably when recipients open emails
3. No external uptime monitoring service is needed

## Database Integration

The server supports both an in-memory database (for development) and Supabase (for production):

### Supabase Integration

The application includes built-in support for Supabase as a persistent database:

1. **Setup Supabase**:
   - Create an account at [supabase.com](https://supabase.com)
   - Create a new project
   - Run the SQL setup script in `supabase_setup.sql` in the Supabase SQL Editor
   - Copy your project URL and anon key

2. **Configure Environment**:
   - Copy `.env.example` to `.env`
   - Update with your Supabase URL and key:
     ```
     SUPABASE_URL=https://your-project-id.supabase.co
     SUPABASE_KEY=your-supabase-anon-key
     ```

3. **Database Schema**:
   The Supabase schema includes:
   - `email_accounts`: Stores your 5 email accounts
   - `recipients`: Stores recipient information
   - `email_queue`: Acts as a queue for scheduled emails, including follow-ups
   - `email_tracking`: Stores tracking data for opens
   - `daily_schedules`: Stores daily email schedules
   - `link_tracking`: Tracks link clicks
   - `server_status`: Monitors server health

   The schema includes support for follow-up emails with:
   - Tracking which emails need follow-ups
   - Storing follow-up sequence numbers (1st follow-up, 2nd follow-up)
   - Linking follow-up emails to their original emails
   - Configurable follow-up delays

4. **Multi-Account Support**:
   - The schema supports 5 separate email accounts
   - Each account can send N emails daily (default: 10)
   - Emails are scheduled randomly between 9am and 5pm

5. **Example Usage**:
   Run the example script to see how to use the Supabase integration:
   ```
   python examples/supabase_example.py
   ```
   This will create sample accounts, recipients, and schedules in your database.

### Other Database Options

If you prefer not to use Supabase, you can:

1. **Use SQLite**: Modify `app/tracking/database.py` to use SQLite
2. **Use MongoDB Atlas**: Integrate with MongoDB Atlas for document storage

## Security Considerations

- The app password is provided via command line to avoid storing it in code
- Use HTTPS for all production deployments
- Consider IP rate limiting for production use
- Be aware of email privacy laws in your jurisdiction

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with Python standard libraries
- Uses Zoho Mail for SMTP services
