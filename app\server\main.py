#!/usr/bin/env python3
"""
Main server module for the Cold Email Server
"""
import socketserver
import threading

from app.config import SERVER_CONFIG, EMAIL_CONFIG
from app.utils.logger import setup_logger
from app.utils.keep_alive import start_ping_pong_cycle, stop_ping_pong_cycle
from app.tracking.handlers import test_tracking_pixel
from app.server.handlers import EmailHandler
from app.server.queue import start_email_processor, stop_email_processor, setup_test_email

# Set up logger
logger = setup_logger('server.main')

def run_server(host=None, port=None, keep_alive=True, process_emails_enabled=True):
    """
    Run the HTTP server.

    Args:
        host (str, optional): Host to bind to
        port (int, optional): Port to bind to
        keep_alive (bool, optional): Whether to start the keep-alive mechanism
        process_emails_enabled (bool, optional): Whether to process scheduled emails
    """
    # Use configuration values if not provided
    if host is None:
        host = SERVER_CONFIG['host']
    if port is None:
        port = SERVER_CONFIG['port']

    # Print some helpful information
    logger.info(f"Server starting with configuration:")
    logger.info(f"  - Host: {host}")
    logger.info(f"  - Port: {port}")
    logger.info(f"  - Base URL: {SERVER_CONFIG['base_url']}")
    logger.info(f"  - Keep-alive: {'enabled' if keep_alive else 'disabled'}")
    logger.info(f"  - Email processing: {'enabled' if process_emails_enabled else 'disabled'}")
    logger.info(f"  - Follow-up emails: {'enabled' if EMAIL_CONFIG['send_follow_ups'] else 'disabled'}")
    logger.info(f"  - Test mode: {'enabled' if SERVER_CONFIG['test_mode'] else 'disabled'}")

    # Create test tracking pixels
    test_tracking_pixel()

    # Set up test email if in test mode
    if SERVER_CONFIG['test_mode']:
        setup_test_email()

    # Start the server
    with socketserver.TCPServer((host, port), EmailHandler) as httpd:
        logger.info(f"Server started at {SERVER_CONFIG['base_url']}")
        logger.info(f"Tracking statistics available at {SERVER_CONFIG['base_url']}/tracking")

        # Start the ping-pong cycle to keep the server awake on Render
        if keep_alive:
            logger.info("Starting keep-alive mechanism to prevent server sleep")
            start_ping_pong_cycle()

        # Start the email processor (which now handles follow-ups automatically)
        if process_emails_enabled:
            logger.info("Starting scheduled email processor")

            # In test mode, we still pass the normal interval, but the processor will use 20 seconds internally
            if SERVER_CONFIG['test_mode']:
                logger.info("Test mode: Database fetch interval will be 20 seconds instead of 2 minutes")

            # Fetch every 2 minutes (or 20 seconds in test mode), but look ahead 60 minutes
            start_email_processor(interval_minutes=2, look_ahead_minutes=60)

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")

            # Properly shut down all components
            if process_emails_enabled:
                logger.info("Stopping email processor...")
                stop_email_processor()

            if keep_alive:
                logger.info("Stopping keep-alive mechanism...")
                stop_ping_pong_cycle()

            # Close the server
            httpd.server_close()
            logger.info("Server shutdown complete")
