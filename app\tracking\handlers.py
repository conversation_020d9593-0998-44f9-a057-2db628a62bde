#!/usr/bin/env python3
"""
Handlers for tracking endpoints
"""
import json
import datetime
from app.utils.logger import setup_logger
from app.tracking.database import record_email_open as record_email_open_memory
from app.tracking.database import get_email_data, get_tracking_summary
from app.tracking.pixels import get_tracking_pixel_data, generate_test_pixels
from app.database.supabase_client import db

# Set up logger
logger = setup_logger('tracking.handlers')

def handle_tracking_pixel(path, headers=None, client_address=None):
    """
    Handle a request for a tracking pixel.

    Args:
        path (str): Request path
        headers (dict, optional): Request headers
        client_address (tuple, optional): Client address (host, port)

    Returns:
        tuple: (status_code, content_type, content)
    """
    # Parse the path to get the tracking ID and pixel type
    parts = path.split('/')
    if len(parts) < 4:
        return 404, 'text/plain', b'Not Found'

    tracking_id = parts[2]
    pixel_filename = parts[3].split('?')[0]  # Remove query parameters

    # Determine the pixel type based on the filename
    pixel_type = 'transparent'
    if pixel_filename == 'pixel.gif':
        pixel_type = 'red'
    elif pixel_filename == 'blue.gif':
        pixel_type = 'blue'
    elif pixel_filename == 'logo.gif':
        pixel_type = 'logo'

    # Get user agent and IP address
    user_agent = headers.get('User-Agent') if headers else None
    ip_address = client_address[0] if client_address else None

    # Record the email open in both in-memory database and Supabase
    try:
        # First try to record in Supabase
        supabase_result = db.record_email_open(tracking_id, user_agent, ip_address)
        if supabase_result:
            logger.info(f"Successfully recorded email open in Supabase for tracking ID: {tracking_id}")
        else:
            logger.warning(f"Failed to record email open in Supabase for tracking ID: {tracking_id}")
    except Exception as e:
        logger.error(f"Error recording email open in Supabase: {str(e)}")

    # Also record in in-memory database as a backup
    record_email_open_memory(tracking_id, user_agent, ip_address)

    # Return the pixel data
    return 200, 'image/gif', get_tracking_pixel_data(pixel_type)

def handle_tracking_stats(path):
    """
    Handle a request for tracking statistics.

    Args:
        path (str): Request path

    Returns:
        tuple: (status_code, content_type, content)
    """
    # Check if a specific tracking ID is requested
    parts = path.split('/')
    if len(parts) >= 3:
        tracking_id = parts[2]

        try:
            # First try to get tracking data from Supabase
            tracking_records = db.get_email_tracking_data(tracking_id=tracking_id)

            if tracking_records and len(tracking_records) > 0:
                # Get email details
                email_id = tracking_records[0]['email_id']
                email = db.get_email_by_id(email_id)

                if email:
                    # Get recipient details
                    recipient_id = email.get('recipient_id')
                    recipient_response = db.client.table('recipients').select('*').eq('id', recipient_id).execute()
                    recipient_data = db._handle_response(recipient_response)
                    recipient_email = recipient_data[0]['email'] if recipient_data and len(recipient_data) > 0 else "Unknown"

                    # Format tracking data
                    open_details = []
                    for record in tracking_records:
                        open_details.append({
                            "time": record['open_time'],
                            "user_agent": record['user_agent'],
                            "ip_address": record['ip_address'],
                            "device_type": record.get('device_type'),
                            "browser": record.get('browser'),
                            "os": record.get('os')
                        })

                    response = {
                        "tracking_id": tracking_id,
                        "email_id": email_id,
                        "recipient": recipient_email,
                        "subject": email.get('subject'),
                        "sent_time": email.get('sent_time'),
                        "opens": len(tracking_records),
                        "open_details": open_details,
                        "data_source": "supabase"
                    }

                    logger.info(f"Retrieved tracking data from Supabase for tracking ID: {tracking_id}")
                    return 200, 'application/json', json.dumps(response).encode('utf-8')

            # If no data in Supabase, try in-memory database as fallback
            email_data = get_email_data(tracking_id)
            if email_data:
                response = {
                    "tracking_id": tracking_id,
                    "recipient": email_data['recipient'],
                    "subject": email_data['subject'],
                    "sent_time": email_data['sent_time'],
                    "opens": len(email_data['opens']),
                    "open_details": email_data['opens'],
                    "data_source": "in-memory"
                }
                logger.info(f"Retrieved tracking data from in-memory database for tracking ID: {tracking_id}")
                return 200, 'application/json', json.dumps(response).encode('utf-8')

            # No data found in either database
            response = {"error": f"No tracking data found for ID: {tracking_id}"}
            logger.warning(f"No tracking data found for tracking ID: {tracking_id}")
            return 404, 'application/json', json.dumps(response).encode('utf-8')

        except Exception as e:
            logger.error(f"Error retrieving tracking data: {str(e)}")
            response = {"error": f"Error retrieving tracking data: {str(e)}"}
            return 500, 'application/json', json.dumps(response).encode('utf-8')

    # Return summary of all tracking data
    try:
        # Get summary from Supabase
        supabase_summary = db.get_tracking_summary()

        # Get summary from in-memory database
        memory_summary = get_tracking_summary()

        # Combine the summaries
        combined_summary = {
            "supabase": supabase_summary,
            "in_memory": memory_summary,
            "timestamp": datetime.datetime.now().isoformat()
        }

        logger.info("Retrieved tracking summary from both databases")
        return 200, 'application/json', json.dumps(combined_summary).encode('utf-8')

    except Exception as e:
        logger.error(f"Error retrieving tracking summary: {str(e)}")
        response = {"error": f"Error retrieving tracking summary: {str(e)}"}
        return 500, 'application/json', json.dumps(response).encode('utf-8')

def test_tracking_pixel(tracking_id=None):
    """
    Test the tracking pixel by generating test URLs.

    Args:
        tracking_id (str, optional): Tracking ID to use

    Returns:
        dict: Dictionary with tracking ID and URLs for different pixel types
    """
    # Generate test pixels
    pixel_data = generate_test_pixels(tracking_id)

    # Log the test data
    logger.info("Test tracking pixels created:")
    logger.info(f"  - Tracking ID: {pixel_data['tracking_id']}")
    logger.info(f"  - Red Box URL: {pixel_data['red_url']}")
    logger.info(f"  - Blue Box URL: {pixel_data['blue_url']}")
    logger.info(f"  - Logo URL: {pixel_data['logo_url']}")
    logger.info(f"  - Transparent URL: {pixel_data['transparent_url']}")
    logger.info(f"  - Check URL: {pixel_data['check_url']}")
    logger.info(f"  - To test tracking, open any of these URLs in your browser")

    return pixel_data
