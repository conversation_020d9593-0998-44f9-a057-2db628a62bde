#!/usr/bin/env python3
"""
Logging configuration for the Cold Email Server
"""
import logging

def setup_logger(name='cold_email_server', level=logging.INFO):
    """
    Set up and return a logger with the specified name and level.
    
    Args:
        name (str): The name of the logger
        level (int): The logging level (e.g., logging.INFO, logging.DEBUG)
        
    Returns:
        logging.Logger: Configured logger instance
    """
    # Configure logging format
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Get and return the logger
    return logging.getLogger(name)
