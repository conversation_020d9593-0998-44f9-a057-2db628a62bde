#!/usr/bin/env python3
"""
Tests for the database module
"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import datetime
from typing import Dict, Any

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.test_config import BaseTestCase, MOCK_DB
from app.database.supabase_client import SupabaseClient

class MockResponse:
    """Mock response object for Supabase client."""
    
    def __init__(self, data=None, error=None):
        self.data = data
        self.error = error

class TestDatabase(BaseTestCase):
    """Test cases for the database module."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Create a SupabaseClient instance for testing
        self.db = SupabaseClient(
            url=os.environ.get('SUPABASE_URL'),
            key=os.environ.get('SUPABASE_KEY')
        )
        
        # Set up mock responses
        self.mock_response = MockResponse(data=MOCK_DB['email_accounts'])
        self.mock_client.table.return_value.select.return_value.execute.return_value = self.mock_response
        self.mock_client.table.return_value.insert.return_value.execute.return_value = self.mock_response
        self.mock_client.table.return_value.update.return_value.eq.return_value.execute.return_value = self.mock_response
        self.mock_client.rpc.return_value.execute.return_value = self.mock_response
    
    def test_init(self):
        """Test initialization of SupabaseClient."""
        self.assertEqual(self.db.url, os.environ.get('SUPABASE_URL'))
        self.assertEqual(self.db.key, os.environ.get('SUPABASE_KEY'))
        self.assertEqual(self.db.client, self.mock_client)
    
    def test_is_connected(self):
        """Test is_connected method."""
        self.assertTrue(self.db.is_connected())
    
    def test_handle_response(self):
        """Test _handle_response method."""
        # Test successful response
        response = MockResponse(data=MOCK_DB['email_accounts'])
        result = self.db._handle_response(response)
        self.assertEqual(result, MOCK_DB['email_accounts'])
        
        # Test error response
        response = MockResponse(error="Test error")
        result = self.db._handle_response(response)
        self.assertIsNone(result)
    
    def test_get_email_accounts(self):
        """Test get_email_accounts method."""
        # Set up mock response
        self.mock_response.data = MOCK_DB['email_accounts']
        
        # Call the method
        result = self.db.get_email_accounts()
        
        # Check that the correct method was called
        self.mock_client.table.assert_called_with('email_accounts')
        self.mock_client.table.return_value.select.assert_called_with('*')
        
        # Check the result
        self.assertEqual(result, MOCK_DB['email_accounts'])
    
    def test_create_email_account(self):
        """Test create_email_account method."""
        # Set up mock response
        account_data = {
            'id': '********-0000-0000-0000-********0005',
            'email': '<EMAIL>',
            'display_name': 'New User',
            'smtp_server': 'smtp.zoho.eu',
            'smtp_port': 465,
            'use_ssl': True,
            'daily_limit': 10,
            'active': True
        }
        self.mock_response.data = [account_data]
        
        # Call the method
        result = self.db.create_email_account(
            email='<EMAIL>',
            display_name='New User'
        )
        
        # Check that the correct method was called
        self.mock_client.table.assert_called_with('email_accounts')
        
        # Check the result
        self.assertEqual(result, [account_data])
    
    def test_get_recipients(self):
        """Test get_recipients method."""
        # Set up mock response
        self.mock_response.data = MOCK_DB['recipients']
        
        # Call the method
        result = self.db.get_recipients()
        
        # Check that the correct method was called
        self.mock_client.table.assert_called_with('recipients')
        
        # Check the result
        self.assertEqual(result, MOCK_DB['recipients'])
    
    def test_create_recipient(self):
        """Test create_recipient method."""
        # Set up mock response
        recipient_data = {
            'id': '********-0000-0000-0000-************',
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'Recipient',
            'company': 'New Company',
            'status': 'active'
        }
        self.mock_response.data = [recipient_data]
        
        # Call the method
        result = self.db.create_recipient(
            email='<EMAIL>',
            first_name='New',
            last_name='Recipient',
            company='New Company'
        )
        
        # Check that the correct method was called
        self.mock_client.table.assert_called_with('recipients')
        
        # Check the result
        self.assertEqual(result, [recipient_data])
    
    def test_queue_email(self):
        """Test queue_email method."""
        # Set up mock response
        email_data = {
            'id': '********-0000-0000-0000-********0007',
            'account_id': '********-0000-0000-0000-********0001',
            'recipient_id': '********-0000-0000-0000-********0002',
            'subject': 'New Test Email',
            'body': '<html><body><p>New test email body</p></body></html>',
            'scheduled_time': '2023-01-02T12:00:00Z',
            'status': 'scheduled'
        }
        self.mock_response.data = [email_data]
        
        # Call the method
        result = self.db.queue_email(
            account_id='********-0000-0000-0000-********0001',
            recipient_id='********-0000-0000-0000-********0002',
            subject='New Test Email',
            body='<html><body><p>New test email body</p></body></html>',
            scheduled_time=datetime.datetime(2023, 1, 2, 12, 0, 0)
        )
        
        # Check that the correct method was called
        self.mock_client.table.assert_called_with('email_queue')
        
        # Check the result
        self.assertEqual(result, [email_data])
    
    def test_schedule_follow_up(self):
        """Test schedule_follow_up method."""
        # Set up mock response
        follow_up_data = {
            'id': '********-0000-0000-0000-********0008'
        }
        self.mock_response.data = follow_up_data
        
        # Call the method
        result = self.db.schedule_follow_up(
            email_id='********-0000-0000-0000-********0003',
            follow_up_days=3
        )
        
        # Check that the correct method was called
        self.mock_client.rpc.assert_called_with('schedule_follow_up_email', {
            'email_id': '********-0000-0000-0000-********0003',
            'follow_up_days': 3
        })
        
        # Check the result
        self.assertEqual(result, follow_up_data)

if __name__ == '__main__':
    unittest.main()
