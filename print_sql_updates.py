#!/usr/bin/env python3
"""
Print SQL updates to fix the database functions for email content
"""
import os
import sys
import argparse
from app.utils.logger import setup_logger

# Set up logger
logger = setup_logger('update_script')

def print_sql_file(file_path):
    """
    Print the contents of a SQL file.
    
    Args:
        file_path (str): Path to the SQL file
    """
    try:
        # Read the SQL file
        with open(file_path, 'r') as f:
            sql = f.read()
            
        # Print the SQL
        print(f"\n--- SQL from {file_path} ---\n")
        print(sql)
        print("\n--- End of SQL ---\n")
        
    except Exception as e:
        logger.error(f"Error reading SQL file: {str(e)}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Print SQL updates for email content')
    parser.add_argument('--files', nargs='+', default=['update_get_emails_to_send.sql', 'update_schedule_follow_up_email.sql'], 
                        help='Paths to the SQL files')
    
    args = parser.parse_args()
    
    for file_path in args.files:
        # Check if the file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            continue
            
        # Print the SQL file
        print_sql_file(file_path)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
